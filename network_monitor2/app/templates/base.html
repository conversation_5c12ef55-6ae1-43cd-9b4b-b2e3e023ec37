<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量分析系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        
        .dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        
        .dark-mode .sidebar {
            background-color: #1e1e1e;
        }
        
        .dark-mode .nav-link {
            color: #e0e0e0;
        }
        
        .dark-mode .nav-link:hover {
            background-color: #2d2d2d;
        }
        
        .dark-mode .nav-link.active {
            background-color: #0d6efd;
        }
        
        .dark-mode .card {
            background-color: #2d2d2d;
            color: #e0e0e0;
        }
        
        .dark-mode .table {
            color: #e0e0e0;
        }
        
        .dark-mode-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        .nav-link {
            color: #333;
            padding: 10px 20px;
        }
        
        .nav-link:hover {
            background-color: #e9ecef;
        }
        
        .nav-link.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <nav class="sidebar col-md-3 col-lg-2">
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/">
                        <i class="fas fa-chart-line"></i> 数据看板
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/anomaly-detection">
                        <i class="fas fa-exclamation-triangle"></i> 异常检测
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/encryption-detection">
                        <i class="fas fa-lock"></i> 加密检测
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/plotly.js/2.5.1/plotly.min.js"></script>
    <button class="btn btn-secondary dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i> 黑暗模式
    </button>
    <script>
        $(document).ready(function() {
            const darkModeToggle = $('#darkModeToggle');
            
            // 检查本地存储
            if (localStorage.getItem('darkMode') === 'enabled') {
                $('body').addClass('dark-mode');
                darkModeToggle.html('<i class="fas fa-sun"></i> 明亮模式');
            }
            
            // 切换事件
            darkModeToggle.click(function() {
                $('body').toggleClass('dark-mode');
                
                if ($('body').hasClass('dark-mode')) {
                    localStorage.setItem('darkMode', 'enabled');
                    darkModeToggle.html('<i class="fas fa-sun"></i> 明亮模式');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                    darkModeToggle.html('<i class="fas fa-moon"></i> 黑暗模式');
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>