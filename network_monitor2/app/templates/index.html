{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">网络流量监控</h1>
    
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">实时流量捕获</h5>
                    <div>
                        <button id="save-capture-btn" class="btn btn-success" disabled>
                            <i class="fas fa-save me-1"></i> 保存为PCAP
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select class="form-select" id="interface-select">
                                    <option value="">选择网络接口...</option>
                                </select>
                                <button class="btn btn-primary" id="start-capture-btn">开始捕获</button>
                                <button class="btn btn-danger" id="stop-capture-btn" disabled>停止捕获</button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-primary rounded-pill" id="status-badge">就绪</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5>实时流量统计</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">总数据包数</h5>
                                            <h3 id="total-packets">0</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">总流量</h5>
                                            <h3 id="total-bytes">0 KB</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>数据包详情</h5>
                            <div class="table-responsive">
                                <table class="table table-hover" id="packet-table">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>时间</th>
                                            <th>源地址</th>
                                            <th>目标地址</th>
                                            <th>源端口</th>
                                            <th>目标端口</th>
                                            <th>协议</th>
                                            <th>长度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 数据包信息将在这里动态添加 -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="no-packets-message" class="alert alert-info text-center">
                                开始捕获数据包后，详细信息将在这里显示
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">流量趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="traffic-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存PCAP文件模态框 -->
<div class="modal fade" id="save-pcap-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">保存捕获流量</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="save-pcap-form">
                    <div class="mb-3">
                        <label for="pcap-filename" class="form-label">文件名</label>
                        <input type="text" class="form-control" id="pcap-filename">
                        <div class="form-text">文件将自动添加.pcap扩展名</div>
                    </div>
                    <div class="mb-3">
                        <label for="save-location" class="form-label">保存位置</label>
                        <select class="form-select" id="save-location">
                            <option value="default">默认位置 (app/uploads)</option>
                            <option value="downloads">下载文件夹</option>
                            <option value="custom">自定义位置</option>
                        </select>
                    </div>
                    <div class="mb-3" id="custom-path-group" style="display: none;">
                        <label for="custom-path" class="form-label">自定义路径</label>
                        <input type="text" class="form-control" id="custom-path" placeholder="/path/to/save/directory">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="save-pcap-confirm">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let isCapturing = false;
    let packetCount = 0;
    let lastPacketIndex = 0;
    let trafficChart;
    let trafficData = {
        labels: [],
        datasets: [{
            label: '流量 (bytes/s)',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    };
    
    // 初始化图表
    function initChart() {
        const ctx = document.getElementById('traffic-chart').getContext('2d');
        trafficChart = new Chart(ctx, {
            type: 'line',
            data: trafficData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });
    }
    
    // 更新图表
    function updateChart(timestamp, bytesPerSecond) {
        if (trafficData.labels.length > 10) {
            trafficData.labels.shift();
            trafficData.datasets[0].data.shift();
        }
        
        trafficData.labels.push(timestamp);
        trafficData.datasets[0].data.push(bytesPerSecond);
        
        trafficChart.update();
    }
    
    // 获取网络接口列表
    function getNetworkInterfaces() {
        $.get('/api/network-interfaces', function(response) {
            if (response.success) {
                const select = $('#interface-select');
                select.empty();
                select.append('<option value="">选择网络接口...</option>');
                
                response.interfaces.forEach(iface => {
                    select.append(`<option value="${iface.name}">${iface.description}</option>`);
                });
            } else {
                alert('获取网络接口失败: ' + response.error);
            }
        });
    }
    
    // 获取捕获统计信息
    function getCaptureStats() {
        if (isCapturing) {
            $.get('/api/capture-stats', function(response) {
                if (response.success) {
                    // 更新统计数据
                    $('#total-packets').text(response.total_packets);
                    
                    // 格式化字节大小
                    let bytesText;
                    if (response.total_bytes < 1024) {
                        bytesText = response.total_bytes + ' B';
                    } else if (response.total_bytes < 1024 * 1024) {
                        bytesText = (response.total_bytes / 1024).toFixed(2) + ' KB';
                    } else {
                        bytesText = (response.total_bytes / (1024 * 1024)).toFixed(2) + ' MB';
                    }
                    $('#total-bytes').text(bytesText);
                    
                    // 更新图表
                    const now = new Date().toLocaleTimeString();
                    updateChart(now, response.bytes_per_second || 0);
                    
                    // 如果有数据包，启用保存按钮
                    if (response.total_packets > 0) {
                        $('#save-capture-btn').prop('disabled', false);
                    }
                    
                    // 递归调用，每秒更新一次
                    setTimeout(getCaptureStats, 1000);
                }
            });
        }
    }
    
    // 获取最新数据包
    function getLatestPackets() {
        if (isCapturing) {
            $.get('/api/latest-packets', { since: lastPacketIndex }, function(response) {
                if (response.success) {
                    // 隐藏提示信息
                    $('#no-packets-message').hide();
                    
                    // 更新数据包表格
                    const tbody = $('#packet-table tbody');
                    
                    response.packets.forEach(packet => {
                        // 添加到表格
                        tbody.append(`
                            <tr>
                                <td>${packet.id}</td>
                                <td>${packet.time}</td>
                                <td>${packet.src_ip}</td>
                                <td>${packet.dst_ip}</td>
                                <td>${packet.src_port}</td>
                                <td>${packet.dst_port}</td>
                                <td>${packet.protocol}</td>
                                <td>${packet.length}</td>
                            </tr>
                        `);
                    });
                    
                    // 如果表格行数超过100，删除旧的行
                    if (tbody.children().length > 100) {
                        tbody.children().slice(0, tbody.children().length - 100).remove();
                    }
                    
                    // 更新下一次请求的起始索引
                    lastPacketIndex = response.next_index;
                    
                    // 递归调用，每秒更新一次
                    setTimeout(getLatestPackets, 1000);
                }
            });
        }
    }
    
    // 开始捕获
    $('#start-capture-btn').click(function() {
        const interface = $('#interface-select').val();
        if (!interface) {
            alert('请选择网络接口');
            return;
        }
        
        $.ajax({
            url: '/api/start-capture',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ interface: interface }),
            success: function(response) {
                if (response.success) {
                    isCapturing = true;
                    
                    // 更新UI状态
                    $('#start-capture-btn').prop('disabled', true);
                    $('#stop-capture-btn').prop('disabled', false);
                    $('#status-badge').text('捕获中').removeClass('bg-primary').addClass('bg-danger');
                    
                    // 重置统计数据
                    lastPacketIndex = 0;
                    $('#packet-table tbody').empty();
                    
                    // 开始定时获取统计信息
                    getCaptureStats();
                    getLatestPackets();
                } else {
                    alert('启动捕获失败: ' + response.error);
                }
            },
            error: function(xhr) {
                alert('启动捕获失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
            }
        });
    });
    
    // 停止捕获
    $('#stop-capture-btn').click(function() {
        $.ajax({
            url: '/api/stop-capture',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    isCapturing = false;
                    
                    // 更新UI状态
                    $('#start-capture-btn').prop('disabled', false);
                    $('#stop-capture-btn').prop('disabled', true);
                    $('#status-badge').text('已停止').removeClass('bg-danger').addClass('bg-primary');
                } else {
                    alert('停止捕获失败: ' + response.error);
                }
            },
            error: function(xhr) {
                alert('停止捕获失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
            }
        });
    });
    
    // 保存位置切换
    $('#save-location').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom-path-group').show();
        } else {
            $('#custom-path-group').hide();
        }
    });
    
    // 保存捕获流量
    $('#save-capture-btn').click(function() {
        // 生成默认文件名
        const date = new Date();
        const timestamp = date.getFullYear() +
            ('0' + (date.getMonth() + 1)).slice(-2) +
            ('0' + date.getDate()).slice(-2) + '_' +
            ('0' + date.getHours()).slice(-2) +
            ('0' + date.getMinutes()).slice(-2) +
            ('0' + date.getSeconds()).slice(-2);
        $('#pcap-filename').val('capture_' + timestamp);
        
        // 显示模态框
        const saveModal = new bootstrap.Modal(document.getElementById('save-pcap-modal'));
        saveModal.show();
    });
    
    // 确认保存按钮点击
    $('#save-pcap-confirm').click(function() {
        // 获取保存选项
        const filename = $('#pcap-filename').val();
        const saveLocation = $('#save-location').val();
        const customPath = $('#custom-path').val();
        
        $.ajax({
            url: '/api/save-capture',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                filename: filename,
                location: saveLocation,
                custom_path: customPath
            }),
            success: function(response) {
                if (response.success) {
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('save-pcap-modal')).hide();
                    alert('捕获流量已成功保存为PCAP文件\n路径: ' + response.file_path);
                } else {
                    alert('保存失败: ' + response.error);
                }
            },
            error: function(xhr) {
                alert('保存失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
            }
        });
    });
    
    // 初始化图表
    initChart();
    
    // 获取网络接口列表
    getNetworkInterfaces();
    
    // 自动调整图表大小
    window.addEventListener('resize', function() {
        if (trafficChart) {
            trafficChart.resize();
        }
    });
});
</script>
{% endblock %}