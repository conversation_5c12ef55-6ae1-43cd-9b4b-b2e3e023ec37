{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <h1 class="h2 mb-4">加密流量检测</h1>
    
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">PCAP文件分析</h5>
                </div>
                <div class="card-body">
                    <form id="pcap-upload-form" class="mb-4">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <input type="file" class="form-control" id="pcap-file" accept=".pcap,.pcapng" required>
                            </div>
                            <div class="col-auto">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>上传并分析
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div id="analysis-progress" class="progress mb-3" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">分析结果</h5>
                    <div class="d-flex align-items-center">
                        <input type="text" id="packet-filter" class="form-control me-2" placeholder="过滤器 (例如: ip.src == ***********)">
                        <button class="btn btn-outline-primary" id="apply-filter">应用过滤器</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info" id="no-data-message">
                        请上传PCAP文件进行分析
                    </div>
                    <div id="results-container" style="display: none;">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">总数据包数</h5>
                                        <h3 id="total-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h5 class="card-title">加密数据包</h5>
                                        <h3 id="encrypted-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">未加密数据包</h5>
                                        <h3 id="unencrypted-packets">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">加密比例</h5>
                                        <h3 id="encryption-ratio">0%</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">加密协议分布</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="encryption-protocols-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">加密流量趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="encryption-trend-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <table class="table table-hover" id="packet-table">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>时间</th>
                                    <th>源地址</th>
                                    <th>目标地址</th>
                                    <th>协议</th>
                                    <th>长度</th>
                                    <th>加密状态</th>
                                    <th>加密类型</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据包详情模态框 -->
<div class="modal fade" id="packet-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">数据包详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="accordion" id="packet-detail-accordion">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let protocolsChart = null;
    let trendChart = null;
    
    // 处理文件上传
    $('#pcap-upload-form').on('submit', function(e) {
        e.preventDefault();
        
        const fileInput = $('#pcap-file')[0];
        if (!fileInput.files.length) {
            alert('请选择PCAP文件');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        
        // 显示进度条
        $('#analysis-progress').show();
        $('#no-data-message').hide();
        $('#results-container').hide();
        
        $.ajax({
            url: '/upload-pcap',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // 分析上传的文件
                    analyzeFile(response.file_path);
                } else {
                    alert('上传失败：' + response.error);
                    $('#analysis-progress').hide();
                    $('#no-data-message').show();
                }
            },
            error: function(xhr) {
                alert('上传失败：' + xhr.responseJSON.error);
                $('#analysis-progress').hide();
                $('#no-data-message').show();
            }
        });
    });
    
    // 分析文件
    function analyzeFile(filePath) {
        $.ajax({
            url: '/api/analyze-encryption',
            type: 'POST',
            data: JSON.stringify({file_path: filePath}),
            contentType: 'application/json',
            success: function(data) {
                updateResults(data);
                $('#analysis-progress').hide();
                $('#results-container').show();
            },
            error: function(xhr) {
                alert('分析失败：' + (xhr.responseJSON ? xhr.responseJSON.error : '未知错误'));
                $('#analysis-progress').hide();
                $('#no-data-message').show();
            }
        });
    }
    
    // 更新结果
    function updateResults(data) {
        // 更新统计信息
        const totalPackets = data.packets.length;
        const encryptedPackets = data.packets.filter(p => p.is_encrypted).length;
        const unencryptedPackets = totalPackets - encryptedPackets;
        const encryptionRatio = ((encryptedPackets / totalPackets) * 100).toFixed(2);
        
        $('#total-packets').text(totalPackets);
        $('#encrypted-packets').text(encryptedPackets);
        $('#unencrypted-packets').text(unencryptedPackets);
        $('#encryption-ratio').text(encryptionRatio + '%');
        
        // 更新加密协议分布图表
        updateProtocolsChart(data.protocol_stats);
        
        // 更新加密流量趋势图表
        updateTrendChart(data.time_series);
        
        // 更新数据包表格
        const tbody = $('#packet-table tbody');
        tbody.empty();
        
        data.packets.forEach(packet => {
            tbody.append(`
                <tr class="${packet.is_encrypted ? 'table-warning' : ''}" data-packet-id="${packet.id}">
                    <td>${packet.number}</td>
                    <td>${packet.time}</td>
                    <td>${packet.src}</td>
                    <td>${packet.dst}</td>
                    <td>${packet.protocol}</td>
                    <td>${packet.length}</td>
                    <td>${packet.is_encrypted ? '加密' : '未加密'}</td>
                    <td>${packet.encryption_type || '-'}</td>
                </tr>
            `);
        });
    }
    
    // 更新加密协议分布图表
    function updateProtocolsChart(protocolStats) {
        const ctx = document.getElementById('encryption-protocols-chart').getContext('2d');
        
        if (protocolsChart) {
            protocolsChart.destroy();
        }
        
        protocolsChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(protocolStats),
                datasets: [{
                    data: Object.values(protocolStats),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }
    
    // 更新加密流量趋势图表
    function updateTrendChart(timeSeries) {
        const ctx = document.getElementById('encryption-trend-chart').getContext('2d');
        
        if (trendChart) {
            trendChart.destroy();
        }
        
        trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: timeSeries.map(point => point.time),
                datasets: [{
                    label: '加密流量比例',
                    data: timeSeries.map(point => point.encryption_ratio),
                    borderColor: '#FF6384',
                    fill: false
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '加密比例 (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                }
            }
        });
    }
    
    // 点击数据包显示详情
    $('#packet-table').on('click', 'tr', function() {
        const packetId = $(this).data('packet-id');
        if (packetId) {
            $.get(`/api/packet-detail/${packetId}`, function(data) {
                const accordion = $('#packet-detail-accordion');
                accordion.empty();
                
                Object.entries(data.layers).forEach(([layer, info], index) => {
                    accordion.append(`
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#layer-${index}">
                                    ${layer}
                                </button>
                            </h2>
                            <div id="layer-${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}"
                                 data-bs-parent="#packet-detail-accordion">
                                <div class="accordion-body">
                                    <pre><code>${JSON.stringify(info, null, 2)}</code></pre>
                                </div>
                            </div>
                        </div>
                    `);
                });
                
                $('#packet-detail-modal').modal('show');
            });
        }
    });
    
    // 应用过滤器
    $('#apply-filter').click(function() {
        const filter = $('#packet-filter').val();
        // TODO: 实现过滤器功能
    });
});
</script>
{% endblock %} 