import torch
import torch.nn as nn
import numpy as np
from scapy.all import IP, TCP, UDP, raw

class CNNLSTMModel(nn.Module):
    def __init__(self, input_size=64, hidden_size=128, num_classes=2):
        super(CNNLSTMModel, self).__init__()
        
        # CNN层
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.pool = nn.MaxPool1d(2)
        self.dropout1 = nn.Dropout(0.25)
        
        # LSTM层
        self.lstm = nn.LSTM(input_size=64, 
                           hidden_size=hidden_size,
                           num_layers=2,
                           batch_first=True,
                           bidirectional=True,
                           dropout=0.25)
        
        # 全连接层
        self.fc1 = nn.Linear(hidden_size * 2, 128)
        self.dropout2 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(128, num_classes)
        
    def forward(self, x):
        # CNN处理
        x = x.unsqueeze(1)  # 添加通道维度
        x = torch.relu(self.conv1(x))
        x = self.pool(x)
        x = torch.relu(self.conv2(x))
        x = self.pool(x)
        x = self.dropout1(x)
        
        # 准备LSTM输入
        x = x.permute(0, 2, 1)  # 调整维度顺序
        
        # LSTM处理
        lstm_out, _ = self.lstm(x)
        lstm_out = lstm_out[:, -1, :]  # 取最后一个时间步的输出
        
        # 全连接层处理
        x = torch.relu(self.fc1(lstm_out))
        x = self.dropout2(x)
        x = self.fc2(x)
        
        return x

class NetworkTrafficAnalyzer:
    def __init__(self):
        self.model = CNNLSTMModel()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
    def preprocess_packet(self, packet_data):
        """将数据包转换为模型输入格式"""
        # 提取特征
        features = self.extract_features(packet_data)
        # 转换为tensor
        if features is not None:
            x = torch.FloatTensor(features).to(self.device)
            return x
        return None
    
    def extract_features(self, packet_data):
        """从数据包中提取特征"""
        if packet_data is None:
            return None
            
        try:
            features = []
            
            # 1. 包大小
            features.append(len(packet_data))
            
            # 2. 包头信息
            if IP in packet_data:
                features.extend([
                    packet_data[IP].len,
                    packet_data[IP].id,
                    packet_data[IP].flags,
                    packet_data[IP].ttl
                ])
            else:
                # 如果没有IP层，添加默认值
                features.extend([0, 0, 0, 0])
            
            # 3. 协议类型（one-hot编码）
            protocol_features = [0] * 3  # TCP, UDP, Other
            if TCP in packet_data:
                protocol_features[0] = 1
            elif UDP in packet_data:
                protocol_features[1] = 1
            else:
                protocol_features[2] = 1
            features.extend(protocol_features)
            
            # 4. 端口信息
            if TCP in packet_data:
                features.extend([
                    packet_data[TCP].sport,
                    packet_data[TCP].dport,
                    packet_data[TCP].flags
                ])
            elif UDP in packet_data:
                features.extend([
                    packet_data[UDP].sport,
                    packet_data[UDP].dport,
                    0  # UDP没有flags
                ])
            else:
                # 如果既不是TCP也不是UDP，添加默认值
                features.extend([0, 0, 0])
            
            # 5. 负载统计
            try:
                payload = bytes(packet_data.payload)
                payload_array = np.array([b for b in payload], dtype=np.uint8)
                
                if len(payload_array) > 0:
                    features.extend([
                        len(payload_array),
                        float(np.mean(payload_array)),
                        float(np.std(payload_array))
                    ])
                else:
                    features.extend([0, 0, 0])
            except Exception:
                # 如果无法处理负载，添加默认值
                features.extend([0, 0, 0])
            
            # 确保特征长度一致，如果需要可以填充或截断
            if len(features) < 64:
                features.extend([0] * (64 - len(features)))
            elif len(features) > 64:
                features = features[:64]
                
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            print(f"特征提取错误: {str(e)}")
            return None
    
    def detect_anomaly(self, packet_data):
        """检测异常流量"""
        try:
            self.model.eval()
            with torch.no_grad():
                x = self.preprocess_packet(packet_data)
                if x is not None:
                    output = self.model(x.unsqueeze(0))
                    prediction = torch.argmax(output, dim=1)
                    return bool(prediction.item())
                return False
        except Exception as e:
            print(f"异常检测错误: {str(e)}")
            return False
    
    def detect_encryption(self, packet_data):
        """检测加密流量"""
        try:
            self.model.eval()
            with torch.no_grad():
                x = self.preprocess_packet(packet_data)
                if x is not None:
                    output = self.model(x.unsqueeze(0))
                    prediction = torch.argmax(output, dim=1)
                    return bool(prediction.item())
                return False
        except Exception as e:
            print(f"加密检测错误: {str(e)}")
            return False
    
    def train(self, train_loader, num_epochs=10):
        """训练模型"""
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(self.model.parameters())
        
        self.model.train()
        for epoch in range(num_epochs):
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
    
    def save_model(self, path):
        """保存模型"""
        torch.save(self.model.state_dict(), path)
    
    def load_model(self, path):
        """加载模型"""
        try:
            self.model.load_state_dict(torch.load(path, map_location=self.device))
            self.model.eval()
        except Exception as e:
            print(f"模型加载错误: {str(e)}")
            # 使用随机初始化的模型继续
            pass