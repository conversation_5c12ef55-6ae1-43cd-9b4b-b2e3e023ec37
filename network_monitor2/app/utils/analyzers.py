import os
import json
from datetime import datetime
from scapy.all import rdpcap, IP, TCP, UDP
try:
    from scapy.layers.tls import TLS
except ImportError:
    # 如果TLS模块不可用，创建一个占位符
    class TLSPlaceholder:
        pass
    TLS = TLSPlaceholder

from collections import defaultdict
from .deep_learning_model import NetworkTrafficAnalyzer

# 注意：以下类仅用于提供额外的分析功能，不应与 pcap_analyzer.py 和 flow_analyzer.py 中的类混淆
# 在实际使用中，应优先使用 pcap_analyzer.py 和 flow_analyzer.py 中定义的分析器类

class BasicPcapAnalyzer:
    """基础PCAP文件分析器，用于提供补充功能"""
    def __init__(self):
        self.dl_model = NetworkTrafficAnalyzer()
        
    def extract_basic_info(self, packet, packet_number):
        """提取数据包基本信息"""
        info = {
            'number': packet_number,
            'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
            'length': len(packet),
            'protocol': 'Unknown',
            'src': '',
            'dst': '',
            'layers': {}
        }
        
        # 提取IP层信息
        if IP in packet:
            info['src'] = packet[IP].src
            info['dst'] = packet[IP].dst
            info['protocol'] = packet[IP].proto
            info['layers']['ip'] = {
                'version': packet[IP].version,
                'ihl': packet[IP].ihl,
                'tos': packet[IP].tos,
                'len': packet[IP].len,
                'id': packet[IP].id,
                'flags': packet[IP].flags,
                'frag': packet[IP].frag,
                'ttl': packet[IP].ttl,
                'proto': packet[IP].proto
            }
        
        # 提取TCP/UDP层信息
        if TCP in packet:
            info['protocol'] = 'TCP'
            info['layers']['tcp'] = {
                'sport': packet[TCP].sport,
                'dport': packet[TCP].dport,
                'seq': packet[TCP].seq,
                'ack': packet[TCP].ack,
                'flags': packet[TCP].flags
            }
        elif UDP in packet:
            info['protocol'] = 'UDP'
            info['layers']['udp'] = {
                'sport': packet[UDP].sport,
                'dport': packet[UDP].dport,
                'len': packet[UDP].len
            }
        
        return info

class EnhancedEncryptionDetector:
    """增强的加密流量检测器，提供额外的加密检测功能"""
    def __init__(self):
        self.encryption_protocols = {
            443: 'HTTPS',
            22: 'SSH',
            989: 'FTPS',
            990: 'FTPS',
            993: 'IMAPS',
            995: 'POP3S'
        }
    
    def is_tls_packet(self, packet):
        """检测是否为TLS数据包"""
        try:
            return TLS in packet
        except:
            return False
    
    def detect_encryption_in_packet(self, packet):
        """检测数据包是否加密"""
        if self.is_tls_packet(packet):
            return True, 'TLS/SSL'
            
        if TCP in packet:
            dport = packet[TCP].dport
            sport = packet[TCP].sport
            if dport in self.encryption_protocols:
                return True, self.encryption_protocols[dport]
            if sport in self.encryption_protocols:
                return True, self.encryption_protocols[sport]
                
        return False, None
    
    def analyze_file_for_encryption(self, pcap_file):
        """分析PCAP文件中的加密流量"""
        try:
            packets = rdpcap(pcap_file)
            result = {
                'packets': [],
                'protocol_stats': defaultdict(int),
                'time_series': []
            }
            
            window_size = 100  # 时间序列窗口大小
            window_packets = []
            
            for i, packet in enumerate(packets, 1):
                analyzer = BasicPcapAnalyzer()
                packet_info = analyzer.extract_basic_info(packet, i)
                is_encrypted, encryption_type = self.detect_encryption_in_packet(packet)
                
                packet_info['is_encrypted'] = is_encrypted
                packet_info['encryption_type'] = encryption_type
                
                if is_encrypted and encryption_type:
                    result['protocol_stats'][encryption_type] += 1
                
                result['packets'].append(packet_info)
                window_packets.append(is_encrypted)
                
                # 计算时间序列数据
                if len(window_packets) >= window_size:
                    encrypted_ratio = (sum(window_packets) / window_size) * 100
                    result['time_series'].append({
                        'time': packet_info['time'],
                        'encryption_ratio': encrypted_ratio
                    })
                    window_packets = window_packets[1:]
            
            return result
        except Exception as e:
            print(f"加密分析错误: {str(e)}")
            return {
                'packets': [],
                'protocol_stats': {},
                'time_series': []
            }

class AdvancedAnomalyDetector:
    """高级异常流量检测器，提供更复杂的异常检测功能"""
    def __init__(self):
        self.dl_model = NetworkTrafficAnalyzer()
        self.attack_signatures = {
            'syn_flood': lambda p: TCP in p and p[TCP].flags == 2,  # SYN flag only
            'port_scan': lambda p: TCP in p and p[TCP].flags == 2 and p[TCP].dport in range(1, 1024),
            'ping_flood': lambda p: p.haslayer('ICMP'),
            'udp_flood': lambda p: UDP in p and len(p) >= 1000
        }
    
    def detect_anomaly_in_packet(self, packet):
        """检测数据包是否异常"""
        for attack_type, signature in self.attack_signatures.items():
            try:
                if signature(packet):
                    return True, attack_type
            except:
                continue
                
        # 使用深度学习模型进行检测
        try:
            features = self.dl_model.extract_features(packet)
            if features is not None:
                is_anomaly = self.dl_model.detect_anomaly(packet)
                if is_anomaly:
                    return True, 'ml_detected'
        except:
            pass
                
        return False, None