import dpkt
import datetime
import socket
from scapy.all import *
import pandas as pd
import numpy as np
import psutil
import threading
import queue
import time
from datetime import datetime
import os
import json
from collections import defaultdict

class PcapAnalyzer:
    """PCAP文件分析器"""
    def __init__(self):
        self.flows = {}
        self.common_ports = {
            443: 'HTTPS',
            22: 'SSH',
            989: 'FTPS',
            990: 'FTPS',
            993: 'IMAPS',
            995: 'POP3S'
        }
        
    def get_realtime_traffic(self):
        """获取实时流量数据"""
        # 这里模拟实时数据，实际应用中需要替换为真实的网络接口捕获
        timestamps = pd.date_range(start='now', periods=100, freq='1s')
        traffic = np.random.randint(5000, 40000, size=100)
        return {
            'timestamps': timestamps.strftime('%Y-%m-%d %H:%M:%S').tolist(),
            'traffic': traffic.tolist()
        }
    
    def analyze_file_as_packets(self, file_path):
        """解析PCAP文件并返回单个数据包列表（用于加密检测）"""
        from scapy.all import rdpcap
        packets = rdpcap(file_path)
        packet_list = []
        
        # 处理所有数据包，不进行过滤
        for i, packet in enumerate(packets, 1):
            # 基本信息，无论是什么类型的包都添加
            packet_info = {
                'id': i,
                'number': i,
                'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
                'length': len(packet),
                'protocol': 'Unknown',
                'src_ip': '',
                'dst_ip': '',
                'src_port': 0,
                'dst_port': 0
            }
            
            # 提取IP层信息（如果存在）
            if IP in packet:
                packet_info['src_ip'] = packet[IP].src
                packet_info['dst_ip'] = packet[IP].dst
                
                # 提取TCP/UDP层信息（如果存在）
                if TCP in packet:
                    packet_info['protocol'] = 'TCP'
                    packet_info['src_port'] = packet[TCP].sport
                    packet_info['dst_port'] = packet[TCP].dport
                elif UDP in packet:
                    packet_info['protocol'] = 'UDP'
                    packet_info['src_port'] = packet[UDP].sport
                    packet_info['dst_port'] = packet[UDP].dport
                else:
                    # 其他IP协议
                    if packet[IP].proto == 1:
                        packet_info['protocol'] = 'ICMP'
                    else:
                        packet_info['protocol'] = f'IP({packet[IP].proto})'
            else:
                # 非IP协议包
                if packet.haslayer('ARP'):
                    packet_info['protocol'] = 'ARP'
                    if packet.haslayer('ARP'):
                        arp = packet.getlayer('ARP')
                        packet_info['src_ip'] = arp.psrc
                        packet_info['dst_ip'] = arp.pdst
                elif packet.haslayer('Ether'):
                    packet_info['protocol'] = 'Ethernet'
                    ether = packet.getlayer('Ether')
                    packet_info['src_ip'] = ether.src
                    packet_info['dst_ip'] = ether.dst
            
            # 所有数据包都添加到列表中
            packet_list.append(packet_info)
        
        # 打印统计信息用于调试
        print(f"PCAP文件已解析，共有 {len(packets)} 个数据包，处理后有 {len(packet_list)} 个数据包")
        
        return packet_list
        
    def analyze_file(self, file_path):
        """解析PCAP文件并返回流量数据（按流聚合）"""
        from scapy.all import rdpcap, IP, TCP, UDP
        packets = rdpcap(file_path)
        flows = {}
        
        for packet in packets:
            if IP in packet and (TCP in packet or UDP in packet):
                # 提取流量特征
                ip = packet[IP]
                proto = 'TCP' if TCP in packet else 'UDP'
                
                if proto == 'TCP':
                    sport = packet[TCP].sport
                    dport = packet[TCP].dport
                else:
                    sport = packet[UDP].sport
                    dport = packet[UDP].dport
                
                # 创建流标识
                flow_id = f"{ip.src}:{sport}-{ip.dst}:{dport}-{proto}"
                
                if flow_id not in flows:
                    flows[flow_id] = {
                        'src_ip': ip.src,
                        'dst_ip': ip.dst,
                        'src_port': sport,
                        'dst_port': dport,
                        'protocol': proto,
                        'packet_count': 0,
                        'byte_count': 0,
                        'start_time': packet.time,
                        'end_time': packet.time,
                        'length': len(packet)  # 添加长度字段
                    }
                
                flows[flow_id]['packet_count'] += 1
                flows[flow_id]['byte_count'] += len(packet)
                flows[flow_id]['end_time'] = packet.time
        
        return list(flows.values())
    
    @staticmethod
    def ip_to_str(ip_address):
        """将IP地址转换为字符串格式"""
        try:
            return socket.inet_ntop(socket.AF_INET, ip_address)
        except ValueError:
            return socket.inet_ntop(socket.AF_INET6, ip_address)

    def is_encrypted(self, packet):
        """检测数据包是否可能是加密流量"""
        if TCP in packet:
            dport = packet[TCP].dport
            sport = packet[TCP].sport
            if dport in self.common_ports:
                return True, self.common_ports[dport]
            if sport in self.common_ports:
                return True, self.common_ports[sport]
        return False, None

    def analyze_pcap(self, pcap_file):
        """分析PCAP文件"""
        try:
            packets = rdpcap(pcap_file)
            result = []
            
            for i, packet in enumerate(packets, 1):
                packet_info = self._extract_packet_info(packet, i)
                is_anomaly, attack_type = self._detect_anomaly(packet)
                
                packet_info['is_anomaly'] = is_anomaly
                packet_info['attack_type'] = attack_type
                
                result.append(packet_info)
                
            return result
        except Exception as e:
            raise Exception(f"分析PCAP文件失败: {str(e)}")
            
    def _extract_packet_info(self, packet, packet_number):
        """提取数据包信息"""
        info = {
            'id': packet_number,
            'number': packet_number,
            'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
            'length': len(packet),
            'protocol': 'Unknown',
            'src': '',
            'dst': '',
            'layers': {}
        }
        
        if IP in packet:
            info['src'] = packet[IP].src
            info['dst'] = packet[IP].dst
            info['protocol'] = packet[IP].proto
            info['layers']['ip'] = {
                'version': int(packet[IP].version),
                'ihl': int(packet[IP].ihl),
                'tos': int(packet[IP].tos),
                'len': int(packet[IP].len),
                'id': int(packet[IP].id),
                'flags': int(packet[IP].flags),
                'frag': int(packet[IP].frag),
                'ttl': int(packet[IP].ttl),
                'proto': int(packet[IP].proto)
            }
            
        if TCP in packet:
            info['protocol'] = 'TCP'
            info['layers']['tcp'] = {
                'sport': int(packet[TCP].sport),
                'dport': int(packet[TCP].dport),
                'seq': int(packet[TCP].seq),
                'ack': int(packet[TCP].ack),
                'flags': int(packet[TCP].flags)
            }
            # 添加TCP标志位的可读描述
            flags_desc = []
            if packet[TCP].flags.S: flags_desc.append('SYN')
            if packet[TCP].flags.A: flags_desc.append('ACK')
            if packet[TCP].flags.F: flags_desc.append('FIN')
            if packet[TCP].flags.R: flags_desc.append('RST')
            if packet[TCP].flags.P: flags_desc.append('PSH')
            info['layers']['tcp']['flags_desc'] = ' '.join(flags_desc)
            
        elif UDP in packet:
            info['protocol'] = 'UDP'
            info['layers']['udp'] = {
                'sport': int(packet[UDP].sport),
                'dport': int(packet[UDP].dport),
                'len': int(packet[UDP].len)
            }
            
        return info
        
    def _detect_anomaly(self, packet):
        """检测数据包是否异常"""
        # 检测SYN洪水攻击
        if TCP in packet and packet[TCP].flags == 2:  # SYN flag
            return True, 'SYN Flood'
            
        # 检测端口扫描
        if TCP in packet and packet[TCP].flags == 2 and packet[TCP].dport < 1024:
            return True, 'Port Scan'
            
        # 检测UDP洪水
        if UDP in packet and len(packet) > 1000:
            return True, 'UDP Flood'
            
        # 检测ICMP洪水
        if packet.haslayer('ICMP'):
            return True, 'ICMP Flood'
            
        return False, None

class PacketCapture:
    def __init__(self):
        self.is_capturing = False
        self.captured_packets = []
        self.total_bytes = 0
        self.protocol_stats = defaultdict(int)
        self.capture_thread = None
        
    def get_network_interfaces(self):
        """获取系统网络接口列表"""
        try:
            # 使用psutil获取网络接口信息
            interfaces = []
            for iface, addrs in psutil.net_if_addrs().items():
                # 获取IPv4地址
                ipv4 = next((addr.address for addr in addrs if addr.family == socket.AF_INET), None)
                interfaces.append({
                    'name': iface,
                    'description': f"{iface} ({ipv4})" if ipv4 else iface
                })
            return interfaces
        except Exception as e:
            print(f"获取网络接口失败: {str(e)}")
            return []
            
    def start_capture(self, interface):
        """开始捕获数据包"""
        if self.is_capturing:
            return False
            
        try:
            self.is_capturing = True
            # 在新线程中开始捕获
            self.capture_thread = threading.Thread(
                target=self._capture_packets,
                args=(interface,),
                daemon=True
            )
            self.capture_thread.start()
            return True
        except Exception as e:
            print(f"开始捕获失败: {str(e)}")
            self.is_capturing = False
            return False
            
    def _capture_packets(self, interface):
        """在后台线程中捕获数据包"""
        try:
            sniff(iface=interface, 
                  prn=self._packet_callback, 
                  stop_filter=lambda x: not self.is_capturing)
        except Exception as e:
            print(f"捕获数据包时出错: {str(e)}")
            self.is_capturing = False
            
    def stop_capture(self):
        """停止捕获数据包"""
        self.is_capturing = False
        if self.capture_thread:
            self.capture_thread.join(timeout=1.0)
        return True
        
    def _packet_callback(self, packet):
        """处理捕获的数据包"""
        if not self.is_capturing:
            return
            
        self.captured_packets.append(packet)
        self.total_bytes += len(packet)
        
        if IP in packet:
            proto = packet[IP].proto
            if proto == 6:
                self.protocol_stats['TCP'] += 1
            elif proto == 17:
                self.protocol_stats['UDP'] += 1
            else:
                self.protocol_stats['Other'] += 1
                
    def get_capture_stats(self):
        """获取捕获统计信息"""
        return {
            'total_packets': len(self.captured_packets),
            'total_bytes': self.total_bytes,
            'protocol_stats': dict(self.protocol_stats),
            'bytes_per_second': len(self.captured_packets[-1]) if self.captured_packets else 0
        }

    def get_packet_detail(self, packet_id):
        """获取数据包详细信息"""
        try:
            packet = next(p for p in self.captured_packets if p['id'] == packet_id)
            return self._get_detailed_layers(packet)
        except StopIteration:
            return None
            
    def _get_detailed_layers(self, packet_info):
        """获取数据包各层详细信息"""
        layers = {}
        
        # 添加各层协议信息
        if 'src' in packet_info and 'dst' in packet_info:
            layers['Network Layer'] = {
                'Source IP': packet_info['src'],
                'Destination IP': packet_info['dst'],
                'Protocol': packet_info['protocol']
            }
            
        if 'src_port' in packet_info and 'dst_port' in packet_info:
            layers['Transport Layer'] = {
                'Source Port': packet_info['src_port'],
                'Destination Port': packet_info['dst_port']
            }
            
        return {'layers': layers}
        
    @staticmethod
    def _get_protocol_name(proto):
        """获取协议名称"""
        protocols = {
            1: 'ICMP',
            6: 'TCP',
            17: 'UDP'
        }
        return protocols.get(proto, str(proto))
        
    @staticmethod
    def _get_tcp_flags(tcp):
        """获取TCP标志位信息"""
        flags = []
        if tcp.flags.S: flags.append('SYN')
        if tcp.flags.A: flags.append('ACK')
        if tcp.flags.F: flags.append('FIN')
        if tcp.flags.R: flags.append('RST')
        if tcp.flags.P: flags.append('PSH')
        return '[' + ' '.join(flags) + ']'
        
    def _identify_application_protocol(self, packet):
        """识别应用层协议"""
        if TCP in packet:
            tcp = packet[TCP]
            # 常见端口映射
            port_map = {
                80: 'HTTP',
                443: 'HTTPS',
                21: 'FTP',
                22: 'SSH',
                23: 'Telnet',
                25: 'SMTP',
                53: 'DNS',
                110: 'POP3',
                143: 'IMAP',
                3306: 'MySQL',
                5432: 'PostgreSQL'
            }
            return port_map.get(tcp.dport, port_map.get(tcp.sport, 'TCP'))
        elif UDP in packet:
            udp = packet[UDP]
            port_map = {
                53: 'DNS',
                67: 'DHCP',
                68: 'DHCP',
                69: 'TFTP',
                123: 'NTP',
                161: 'SNMP',
                162: 'SNMP'
            }
            return port_map.get(udp.dport, port_map.get(udp.sport, 'UDP'))
        return 'Unknown'