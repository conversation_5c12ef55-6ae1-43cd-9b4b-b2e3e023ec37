from flask import Blueprint, render_template, jsonify, request, current_app, send_from_directory
from . import db
from .models import PcapFile, NetworkFlow
from .utils.pcap_analyzer import Pcap<PERSON>nalyzer, PacketCapture
from .utils.flow_analyzer import FlowAnalyzer
import plotly.express as px
import pandas as pd
import os
from werkzeug.utils import secure_filename
import json
from collections import defaultdict
import time
from datetime import datetime

main = Blueprint('main', __name__)
packet_capture = PacketCapture()

# 确保上传目录存在
def ensure_upload_dir():
    upload_folder = os.path.join(current_app.root_path, 'uploads')
    os.makedirs(upload_folder, exist_ok=True)
    return upload_folder

@main.route('/')
def index():
    """首页 - 网络流量监控"""
    return render_template('index.html')

@main.route('/api/network-interfaces')
def get_network_interfaces():
    """获取网络接口列表"""
    try:
        interfaces = packet_capture.get_network_interfaces()
        return jsonify({'success': True, 'interfaces': interfaces})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/start-capture', methods=['POST'])
def start_capture():
    """开始捕获数据包"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误'}), 400
            
        data = request.get_json()
        interface = data.get('interface')
        
        if not interface:
            return jsonify({'success': False, 'error': '未指定网络接口'}), 400
            
        if packet_capture.start_capture(interface):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '启动捕获失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/stop-capture', methods=['POST'])
def stop_capture():
    """停止捕获数据包"""
    try:
        if packet_capture.stop_capture():
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '停止捕获失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/capture-stats')
def get_capture_stats():
    """获取捕获统计信息"""
    try:
        stats = packet_capture.get_capture_stats()
        return jsonify({'success': True, **stats})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/latest-packets')
def get_latest_packets():
    """获取最新捕获的数据包信息"""
    try:
        # 获取查询参数
        since = request.args.get('since', 0, type=int)
        limit = request.args.get('limit', 100, type=int)
        
        # 安全检查
        if limit > 1000:
            limit = 1000  # 限制最大返回数量
            
        # 提取最新数据包
        latest_packets = []
        from scapy.all import IP, TCP, UDP
        
        # 确定起始索引
        start_idx = max(0, len(packet_capture.captured_packets) - limit)
        if since > 0 and since < len(packet_capture.captured_packets):
            start_idx = since
            
        # 获取数据包
        for i, packet in enumerate(packet_capture.captured_packets[start_idx:], start_idx):
            packet_info = {
                'id': i,
                'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
                'length': len(packet),
                'protocol': 'Unknown',
                'src_ip': '',
                'dst_ip': '',
                'src_port': '',
                'dst_port': ''
            }
            
            if IP in packet:
                packet_info['src_ip'] = packet[IP].src
                packet_info['dst_ip'] = packet[IP].dst
                
                if TCP in packet:
                    packet_info['protocol'] = 'TCP'
                    packet_info['src_port'] = packet[TCP].sport
                    packet_info['dst_port'] = packet[TCP].dport
                elif UDP in packet:
                    packet_info['protocol'] = 'UDP'
                    packet_info['src_port'] = packet[UDP].sport
                    packet_info['dst_port'] = packet[UDP].dport
                else:
                    # 其他IP协议
                    proto = packet[IP].proto
                    if proto == 1:
                        packet_info['protocol'] = 'ICMP'
                    elif proto == 6:
                        packet_info['protocol'] = 'TCP'
                    elif proto == 17:
                        packet_info['protocol'] = 'UDP'
                    else:
                        packet_info['protocol'] = f'IP({proto})'
            elif packet.haslayer('ARP'):
                packet_info['protocol'] = 'ARP'
                arp = packet.getlayer('ARP')
                packet_info['src_ip'] = arp.psrc if hasattr(arp, 'psrc') else ''
                packet_info['dst_ip'] = arp.pdst if hasattr(arp, 'pdst') else ''
            
            latest_packets.append(packet_info)
            
        return jsonify({
            'success': True, 
            'packets': latest_packets,
            'total': len(packet_capture.captured_packets),
            'next_index': min(start_idx + len(latest_packets), len(packet_capture.captured_packets))
        })
    except Exception as e:
        print(f"获取最新数据包出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/save-capture', methods=['POST'])
def save_capture():
    """保存捕获的流量为PCAP文件"""
    try:
        if not packet_capture.captured_packets:
            return jsonify({'success': False, 'error': '没有捕获的数据包可以保存'}), 400
            
        # 生成文件名，使用当前时间戳
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"capture_{timestamp}.pcap"
        
        # 保存路径
        upload_folder = ensure_upload_dir()
        file_path = os.path.join(upload_folder, filename)
        
        # 保存PCAP文件
        from scapy.all import wrpcap
        wrpcap(file_path, packet_capture.captured_packets)
        
        # 添加到数据库
        pcap_file = PcapFile(
            filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path)
        )
        db.session.add(pcap_file)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'file_path': file_path,
            'message': '捕获流量已保存为PCAP文件'
        })
    except Exception as e:
        print(f"保存捕获流量出错: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/packet-detail/<int:packet_id>')
def get_packet_detail(packet_id):
    """获取数据包详细信息"""
    try:
        packet = NetworkFlow.query.get_or_404(packet_id)
        return jsonify({
            'id': packet.id,
            'time': packet.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f'),
            'layers': json.loads(packet.layers)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@main.route('/upload-pcap', methods=['POST'])
def upload_pcap():
    """上传PCAP文件"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有文件被上传'}), 400
            
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '未选择文件'}), 400
            
        if not file.filename.lower().endswith(('.pcap', '.pcapng')):
            return jsonify({'success': False, 'error': '只支持PCAP/PCAPNG文件'}), 400
            
        # 安全地保存文件
        filename = secure_filename(file.filename)
        upload_folder = ensure_upload_dir()
        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)
        
        # 创建数据库记录
        pcap_file = PcapFile(
            filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path)
        )
        db.session.add(pcap_file)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'file_path': file_path
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/analyze-pcap', methods=['POST'])
def analyze_pcap():
    """分析PCAP文件，检测异常流量"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415
            
        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400
            
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
            
        analyzer = PcapAnalyzer()
        packets = analyzer.analyze_pcap(file_path)
        
        print(f"异常检测分析完成，共有 {len(packets)} 个数据包")
        
        return jsonify(packets)
    except Exception as e:
        print(f"异常检测错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/api/analyze-encryption', methods=['POST'])
def analyze_encryption():
    """分析PCAP文件，检测加密流量"""
    try:
        if not request.is_json:
            return jsonify({'success': False, 'error': '请求格式错误，需要JSON格式'}), 415
            
        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400
            
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
            
        # 使用 PcapAnalyzer 解析文件获取单个数据包数据
        pcap_analyzer = PcapAnalyzer()
        packets = pcap_analyzer.analyze_file_as_packets(file_path)  # 解析PCAP文件为单个数据包列表
        print(f"解析得到 {len(packets)} 个数据包")
        
        # 使用 FlowAnalyzer 检测加密流量
        flow_analyzer = FlowAnalyzer()
        analyzed_packets = flow_analyzer.detect_encryption(packets)  # 传入数据包数据
        print(f"加密分析后有 {len(analyzed_packets)} 个数据包")
        
        # 格式化响应，适配前端需求
        encrypted_packets = [packet for packet in analyzed_packets if packet.get('is_encrypted', False)]
        print(f"其中加密数据包有 {len(encrypted_packets)} 个")
        
        # 按照协议统计
        protocol_stats = defaultdict(int)
        for packet in encrypted_packets:
            encryption_type = packet.get('encryption_type', 'Unknown')
            protocol_stats[encryption_type] += 1
            
        # 创建时间序列数据
        time_series = []
        if analyzed_packets:
            # 为简化示例，我们只提供一个时间点的数据
            encryption_ratio = (len(encrypted_packets) / len(analyzed_packets)) * 100
            time_series.append({
                'time': datetime.now().strftime('%H:%M:%S'),
                'encryption_ratio': encryption_ratio
            })
        
        # 格式化数据包信息以适配前端
        formatted_packets = []
        for packet in analyzed_packets:
            formatted_packets.append({
                'id': packet.get('id', 0),
                'number': packet.get('number', 0),
                'time': packet.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')),
                'src': packet.get('src_ip', ''),
                'dst': packet.get('dst_ip', ''),
                'protocol': packet.get('protocol', 'Unknown'),
                'length': packet.get('length', 0),
                'is_encrypted': packet.get('is_encrypted', False),
                'encryption_type': packet.get('encryption_type', '-')
            })
            
        result = {
            'packets': formatted_packets,
            'protocol_stats': dict(protocol_stats),
            'time_series': time_series
        }
        
        return jsonify(result)
    except Exception as e:
        print(f"加密分析错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@main.route('/pcap-files')
def list_pcap_files():
    """列出所有上传的PCAP文件"""
    files = PcapFile.query.all()
    return jsonify([{
        'id': f.id,
        'filename': f.filename,
        'size': f.file_size,
        'created_at': f.created_at.isoformat()
    } for f in files])

@main.route('/download-pcap/<int:file_id>')
def download_pcap(file_id):
    """下载PCAP文件"""
    pcap_file = PcapFile.query.get_or_404(file_id)
    return send_from_directory(
        os.path.dirname(pcap_file.file_path),
        os.path.basename(pcap_file.file_path),
        as_attachment=True
    )

@main.route('/api/realtime-traffic')
def get_realtime_traffic():
    """获取实时流量数据"""
    analyzer = PcapAnalyzer()
    traffic_data = analyzer.get_realtime_traffic()
    return jsonify(traffic_data)

@main.route('/anomaly-detection')
def anomaly_detection():
    """异常检测页面"""
    return render_template('anomaly_detection.html')

@main.route('/encryption-detection')
def encryption_detection():
    """加密检测页面"""
    return render_template('encryption_detection.html')

@main.route('/api/detect-encryption', methods=['POST'])
def detect_encryption():
    """检测加密流量"""
    try:
        file_path = request.json.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'error': '未提供文件路径'}), 400
            
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
            
        # 首先使用 PcapAnalyzer 解析文件获取单个数据包
        pcap_analyzer = PcapAnalyzer()
        packets = pcap_analyzer.analyze_file_as_packets(file_path)
        
        # 然后使用 FlowAnalyzer 检测加密流量
        flow_analyzer = FlowAnalyzer()
        analyzed_packets = flow_analyzer.detect_encryption(packets)
        
        # 使用与 analyze_encryption 相同的格式化函数
        encrypted_packets = [packet for packet in analyzed_packets if packet.get('is_encrypted', False)]
        
        protocol_stats = defaultdict(int)
        for packet in encrypted_packets:
            encryption_type = packet.get('encryption_type', 'Unknown')
            protocol_stats[encryption_type] += 1
            
        time_series = []
        if analyzed_packets:
            encryption_ratio = (len(encrypted_packets) / len(analyzed_packets)) * 100
            time_series.append({
                'time': datetime.now().strftime('%H:%M:%S'),
                'encryption_ratio': encryption_ratio
            })
        
        formatted_packets = []
        for packet in analyzed_packets:
            formatted_packets.append({
                'id': packet.get('id', 0),
                'number': packet.get('number', 0),
                'time': packet.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')),
                'src': packet.get('src_ip', ''),
                'dst': packet.get('dst_ip', ''),
                'protocol': packet.get('protocol', 'Unknown'),
                'length': packet.get('length', 0),
                'is_encrypted': packet.get('is_encrypted', False),
                'encryption_type': packet.get('encryption_type', '-')
            })
            
        result = {
            'packets': formatted_packets,
            'protocol_stats': dict(protocol_stats),
            'time_series': time_series
        }
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500