from . import db
from datetime import datetime

class PcapFile(db.Model):
    """PCAP文件模型"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    flows = db.relationship('NetworkFlow', backref='pcap_file', lazy=True)

class NetworkFlow(db.Model):
    """网络流量模型"""
    id = db.Column(db.Integer, primary_key=True)
    pcap_file_id = db.Column(db.Integer, db.ForeignKey('pcap_file.id'), nullable=False)
    src_ip = db.Column(db.String(50))
    dst_ip = db.Column(db.String(50))
    src_port = db.Column(db.Integer)
    dst_port = db.Column(db.Integer)
    protocol = db.Column(db.String(10))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    packet_count = db.Column(db.Integer)
    byte_count = db.Column(db.Integer)
    is_anomaly = db.Column(db.Boolean, default=False)
    is_encrypted = db.Column(db.Boolean, default=False)
    attack_type = db.Column(db.String(50))
    application_type = db.Column(db.String(50)) 