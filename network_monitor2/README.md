# 网络流量分析系统

基于Python和Flask的网络流量分析系统，提供实时流量捕获、异常检测和加密流量识别等功能。

## 功能特点

- 🔍 实时网络流量捕获与分析
- 📊 PCAP文件解析与可视化
- 🚨 异常流量检测（SYN洪水、端口扫描等）
- 🔐 加密流量识别
- 📈 流量统计与趋势分析
- 🌐 Web界面实时监控

## 系统要求

- Python 3.6+
- Windows/Linux/MacOS
- 管理员权限（用于网络抓包）

## 安装步骤

1. 克隆仓库：
```bash
git clone https://github.com/yourusername/network-traffic-analyzer.git
cd network-traffic-analyzer
```

2. 创建虚拟环境（推荐）：
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/MacOS
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 启动系统

1. 确保已激活虚拟环境

2. 启动Flask应用：
```bash
# Windows
python run.py
# Linux/MacOS（需要sudo权限进行抓包）
sudo python run.py
```

3. 访问Web界面：
   - 打开浏览器访问 http://localhost:5000

## 使用指南

### 1. 实时流量捕获

1. 在主页面选择网络接口
2. 点击"开始捕获"按钮
3. 实时查看：
   - 流量统计
   - 协议分布
   - 异常检测结果
4. 完成后点击"停止捕获"

### 2. PCAP文件分析

1. 进入"异常检测"或"加密检测"页面
2. 上传PCAP文件：
   - 支持.pcap和.pcapng格式
   - 点击"选择文件"按钮
   - 点击"上传并分析"
3. 查看分析结果：
   - 数据包列表
   - 统计信息
   - 异常标记

### 3. 异常检测

系统自动检测以下异常：
- SYN洪水攻击
- 端口扫描行为
- UDP洪水攻击
- ICMP洪水攻击

### 4. 加密流量识别

自动识别常见加密协议：
- HTTPS (443)
- SSH (22)
- FTPS (989/990)
- IMAPS (993)
- POP3S (995)

## 项目结构

```
network_monitor/
├── app/
│   ├── __init__.py
│   ├── views.py
│   ├── models.py
│   ├── utils/
│   │   ├── pcap_analyzer.py
│   │   └── deep_learning_model.py
│   ├── static/
│   │   ├── css/
│   │   └── js/
│   └── templates/
│       ├── index.html
│       ├── anomaly_detection.html
│       └── encryption_detection.html
├── requirements.txt
├── run.py
└── README.md
```

## 常见问题

1. **无法启动捕获**
   - 确保以管理员权限运行
   - 检查选择的网络接口是否可用

2. **上传文件失败**
   - 确认文件格式（.pcap/.pcapng）
   - 检查文件大小是否超限

3. **分析结果不显示**
   - 检查浏览器控制台错误
   - 确认文件是否损坏

## 安全建议

1. 定期检查异常检测结果
2. 及时处理发现的异常流量
3. 妥善保管捕获的数据文件
4. 定期更新系统和依赖包

## 技术栈

- 后端：Python + Flask
- 数据处理：Scapy + Pandas
- 前端：Bootstrap + jQuery + ECharts
- 机器学习：PyTorch（用于深度学习模型）

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
- 项目主页：[GitHub Repository URL]

## 致谢

感谢以下开源项目：
- Flask
- Scapy
- PyTorch
- ECharts 