"""
数据增强模块 - 提供各种数据增强方法用于训练深度学习模型

包含功能:
- 基于GAN的合成流量数据生成
- 时间掩码增强
- 特征扰动
- 混合采样
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import random
import time
from collections import defaultdict

# 尝试导入可选依赖
try:
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("sklearn 未安装，部分特征工程功能将被禁用")

try:
    from imblearn.over_sampling import SMOTE, ADASYN
    IMBLEARN_AVAILABLE = True
except ImportError:
    IMBLEARN_AVAILABLE = False
    logging.warning("imbalanced-learn 未安装，不平衡数据采样功能将被禁用")

logger = logging.getLogger(__name__)

class TimeSeriesMasking:
    """时间序列掩码增强"""
    
    def __init__(self, mask_ratio=0.2, mask_value=0.0):
        """
        初始化时间序列掩码增强器
        
        Args:
            mask_ratio: 掩码比例，默认0.2表示随机掩盖20%的特征
            mask_value: 掩码值，默认为0.0
        """
        self.mask_ratio = mask_ratio
        self.mask_value = mask_value
    
    def apply(self, features):
        """
        应用时间掩码增强
        
        Args:
            features: 特征张量，形状为 [batch_size, seq_len, feature_dim] 或 [batch_size, feature_dim]
            
        Returns:
            增强后的特征
        """
        if features is None:
            return features
            
        if isinstance(features, np.ndarray):
            features = torch.from_numpy(features).float()
            is_numpy = True
        else:
            is_numpy = False
            
        # 创建掩码
        if features.dim() == 3:  # [batch_size, seq_len, feature_dim]
            batch_size, seq_len, feature_dim = features.shape
            mask = torch.rand(batch_size, seq_len, feature_dim) > self.mask_ratio
        elif features.dim() == 2:  # [batch_size, feature_dim]
            batch_size, feature_dim = features.shape
            mask = torch.rand(batch_size, feature_dim) > self.mask_ratio
        else:
            raise ValueError(f"不支持的特征维度: {features.dim()}")
        
        # 应用掩码
        masked_features = features.clone()
        masked_features[~mask] = self.mask_value
        
        return masked_features.numpy() if is_numpy else masked_features


class FeaturePerturbation:
    """特征扰动增强"""
    
    def __init__(self, noise_level=0.05, perturb_prob=0.3):
        """
        初始化特征扰动增强器
        
        Args:
            noise_level: 噪声水平，默认0.05表示添加5%的高斯噪声
            perturb_prob: 扰动概率，默认0.3表示30%的特征会被扰动
        """
        self.noise_level = noise_level
        self.perturb_prob = perturb_prob
    
    def apply(self, features):
        """
        应用特征扰动增强
        
        Args:
            features: 特征张量，形状为 [batch_size, seq_len, feature_dim] 或 [batch_size, feature_dim]
            
        Returns:
            增强后的特征
        """
        if features is None:
            return features
            
        if isinstance(features, np.ndarray):
            features = torch.from_numpy(features).float()
            is_numpy = True
        else:
            is_numpy = False
            
        # 创建扰动掩码
        if features.dim() == 3:  # [batch_size, seq_len, feature_dim]
            batch_size, seq_len, feature_dim = features.shape
            perturb_mask = torch.rand(batch_size, seq_len, feature_dim) < self.perturb_prob
        elif features.dim() == 2:  # [batch_size, feature_dim]
            batch_size, feature_dim = features.shape
            perturb_mask = torch.rand(batch_size, feature_dim) < self.perturb_prob
        else:
            raise ValueError(f"不支持的特征维度: {features.dim()}")
        
        # 生成噪声
        noise = torch.randn_like(features) * self.noise_level * features.std()
        
        # 应用扰动
        perturbed_features = features.clone()
        perturbed_features[perturb_mask] += noise[perturb_mask]
        
        return perturbed_features.numpy() if is_numpy else perturbed_features


class ImbalancedSampling:
    """不平衡数据采样处理"""
    
    def __init__(self, method='smote', sampling_strategy='auto', random_state=42):
        """
        初始化不平衡数据采样处理器
        
        Args:
            method: 采样方法，可选 'smote', 'adasyn', 'random'
            sampling_strategy: 采样策略，默认'auto'
            random_state: 随机种子
        """
        self.method = method
        self.sampling_strategy = sampling_strategy
        self.random_state = random_state
        self.sampler = None
        
        # 检查是否可用imblearn
        if not IMBLEARN_AVAILABLE and method in ['smote', 'adasyn']:
            logger.warning(f"由于imblearn未安装，将使用随机采样代替{method}")
            self.method = 'random'
        
        # 初始化采样器
        if self.method == 'smote' and IMBLEARN_AVAILABLE:
            self.sampler = SMOTE(sampling_strategy=sampling_strategy, random_state=random_state)
        elif self.method == 'adasyn' and IMBLEARN_AVAILABLE:
            self.sampler = ADASYN(sampling_strategy=sampling_strategy, random_state=random_state)
        elif self.method == 'random':
            self.sampler = None
        else:
            if not IMBLEARN_AVAILABLE:
                self.method = 'random'
                self.sampler = None
            else:
                raise ValueError(f"不支持的采样方法: {method}")
    
    def apply(self, features, labels):
        """
        应用不平衡数据采样
        
        Args:
            features: 特征数组，形状为 [n_samples, n_features]
            labels: 标签数组，形状为 [n_samples]
            
        Returns:
            采样后的特征和标签
        """
        # 如果没有标签或特征，直接返回
        if features is None or labels is None or len(features) == 0 or len(labels) == 0:
            return features, labels
            
        # 转换为numpy数组
        if isinstance(features, torch.Tensor):
            features_np = features.cpu().numpy()
            is_tensor = True
            device = features.device
        else:
            features_np = features
            is_tensor = False
            device = None
            
        if isinstance(labels, torch.Tensor):
            labels_np = labels.cpu().numpy()
            labels_is_tensor = True
            labels_device = labels.device
        else:
            labels_np = labels
            labels_is_tensor = False
            labels_device = None
        
        if self.method == 'random':
            # 随机过采样
            class_counts = defaultdict(int)
            for label in labels_np:
                class_counts[label] += 1
            
            max_count = max(class_counts.values())
            indices_by_class = defaultdict(list)
            
            for i, label in enumerate(labels_np):
                indices_by_class[label].append(i)
            
            resampled_indices = []
            for label, indices in indices_by_class.items():
                # 对少数类进行过采样
                if len(indices) < max_count:
                    resampled_indices.extend(np.random.choice(indices, max_count, replace=True))
                else:
                    resampled_indices.extend(indices)
            
            resampled_features = features_np[resampled_indices]
            resampled_labels = labels_np[resampled_indices]
        elif IMBLEARN_AVAILABLE and self.sampler is not None:
            # 使用SMOTE或ADASYN
            try:
                resampled_features, resampled_labels = self.sampler.fit_resample(features_np, labels_np)
            except Exception as e:
                logger.warning(f"采样失败: {str(e)}，使用原始数据")
                resampled_features, resampled_labels = features_np, labels_np
        else:
            # 如果没有可用的采样器，直接返回原始数据
            resampled_features, resampled_labels = features_np, labels_np
        
        # 转换回原始类型
        if is_tensor:
            resampled_features = torch.from_numpy(resampled_features)
            if device is not None:
                resampled_features = resampled_features.to(device)
        if labels_is_tensor:
            resampled_labels = torch.from_numpy(resampled_labels)
            if labels_device is not None:
                resampled_labels = resampled_labels.to(labels_device)
            
        return resampled_features, resampled_labels


class FlowGAN(nn.Module):
    """基于GAN的网络流量生成器"""
    
    def __init__(self, feature_dim=64, latent_dim=100, hidden_dim=128):
        """
        初始化FlowGAN
        
        Args:
            feature_dim: 特征维度
            latent_dim: 潜在空间维度
            hidden_dim: 隐藏层维度
        """
        super(FlowGAN, self).__init__()
        
        # 生成器
        self.generator = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.BatchNorm1d(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.LeakyReLU(0.2),
            nn.BatchNorm1d(hidden_dim * 2),
            nn.Linear(hidden_dim * 2, feature_dim),
            nn.Tanh()  # 输出范围为[-1, 1]
        )
        
        # 判别器
        self.discriminator = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim * 2),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()  # 输出范围为[0, 1]
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
    
    def generate(self, batch_size, device='cpu'):
        """
        生成合成流量数据
        
        Args:
            batch_size: 批量大小
            device: 设备
            
        Returns:
            生成的特征
        """
        z = torch.randn(batch_size, self.generator[0].in_features, device=device)
        return self.generator(z)
    
    def train_gan(self, dataloader, epochs=100, lr=0.0002, device='cpu'):
        """
        训练GAN
        
        Args:
            dataloader: 数据加载器
            epochs: 训练轮数
            lr: 学习率
            device: 设备
        """
        # 优化器
        optimizer_g = torch.optim.Adam(self.generator.parameters(), lr=lr, betas=(0.5, 0.999))
        optimizer_d = torch.optim.Adam(self.discriminator.parameters(), lr=lr, betas=(0.5, 0.999))
        
        # 损失函数
        criterion = nn.BCELoss()
        
        # 移动模型到设备
        self.to(device)
        
        # 训练循环
        for epoch in range(epochs):
            for i, (real_data, _) in enumerate(dataloader):
                batch_size = real_data.size(0)
                real_data = real_data.to(device)
                
                # 真实数据的标签
                real_labels = torch.ones(batch_size, 1, device=device)
                # 生成数据的标签
                fake_labels = torch.zeros(batch_size, 1, device=device)
                
                # ---------------------
                # 训练判别器
                # ---------------------
                optimizer_d.zero_grad()
                
                # 真实数据的损失
                outputs = self.discriminator(real_data)
                d_loss_real = criterion(outputs, real_labels)
                
                # 生成数据的损失
                z = torch.randn(batch_size, self.generator[0].in_features, device=device)
                fake_data = self.generator(z)
                outputs = self.discriminator(fake_data.detach())
                d_loss_fake = criterion(outputs, fake_labels)
                
                # 总损失
                d_loss = d_loss_real + d_loss_fake
                d_loss.backward()
                optimizer_d.step()
                
                # ---------------------
                # 训练生成器
                # ---------------------
                optimizer_g.zero_grad()
                
                # 生成数据
                outputs = self.discriminator(fake_data)
                g_loss = criterion(outputs, real_labels)  # 生成器希望判别器将生成数据判为真
                
                g_loss.backward()
                optimizer_g.step()
                
            # 打印训练进度
            if (epoch + 1) % 10 == 0:
                logger.info(f'Epoch [{epoch+1}/{epochs}], d_loss: {d_loss.item():.4f}, g_loss: {g_loss.item():.4f}')


class DataAugmentor:
    """数据增强器 - 组合多种增强方法"""
    
    def __init__(self, config=None):
        """
        初始化数据增强器
        
        Args:
            config: 配置字典，包含各种增强方法的参数
        """
        self.config = config or {}
        
        # 初始化各种增强器
        self.masking = TimeSeriesMasking(
            mask_ratio=self.config.get('mask_ratio', 0.2),
            mask_value=self.config.get('mask_value', 0.0)
        )
        
        self.perturbation = FeaturePerturbation(
            noise_level=self.config.get('noise_level', 0.05),
            perturb_prob=self.config.get('perturb_prob', 0.3)
        )
        
        # 检查是否可用imblearn
        sampling_method = self.config.get('sampling_method', 'smote')
        if not IMBLEARN_AVAILABLE and sampling_method in ['smote', 'adasyn']:
            logger.warning(f"由于imblearn未安装，将使用随机采样代替{sampling_method}")
            sampling_method = 'random'
        
        self.sampling = ImbalancedSampling(
            method=sampling_method,
            sampling_strategy=self.config.get('sampling_strategy', 'auto'),
            random_state=self.config.get('random_state', 42)
        )
        
        # GAN模型（如果需要）
        self.gan = None
        if self.config.get('use_gan', False):
            try:
                self.gan = FlowGAN(
                    feature_dim=self.config.get('feature_dim', 64),
                    latent_dim=self.config.get('latent_dim', 100),
                    hidden_dim=self.config.get('hidden_dim', 128)
                )
            except Exception as e:
                logger.warning(f"GAN初始化失败: {str(e)}")
    
    def augment(self, features, labels=None):
        """
        应用数据增强
        
        Args:
            features: 特征数组或张量
            labels: 标签数组（可选）
            
        Returns:
            增强后的特征和标签
        """
        # 如果特征为空，直接返回
        if features is None:
            return features, labels if labels is not None else features
            
        # 转换为numpy数组（如果是张量）
        if isinstance(features, torch.Tensor):
            try:
                features_np = features.cpu().numpy()
                is_tensor = True
                device = features.device
            except Exception as e:
                logger.warning(f"张量转换失败: {str(e)}")
                return features, labels if labels is not None else features
        else:
            features_np = features
            is_tensor = False
            device = None
        
        try:
            # 应用不平衡采样（如果提供了标签）
            if labels is not None and self.config.get('use_sampling', False):
                features_np, labels = self.sampling.apply(features_np, labels)
            
            # 应用时间掩码
            if self.config.get('use_masking', False):
                features_np = self.masking.apply(features_np)
            
            # 应用特征扰动
            if self.config.get('use_perturbation', False):
                features_np = self.perturbation.apply(features_np)
            
            # 使用GAN生成合成数据（如果已训练）
            if self.gan is not None and hasattr(self.gan, 'generator') and self.gan.generator is not None and self.config.get('use_gan_augment', False):
                try:
                    # 生成与原始数据相同数量的合成数据
                    synthetic_features = self.gan.generate(len(features_np), device='cpu').detach().cpu().numpy()
                    
                    # 如果有标签，为合成数据分配标签（这里简单地复制原始标签）
                    if labels is not None:
                        synthetic_labels = np.copy(labels)
                        
                        # 合并原始数据和合成数据
                        features_np = np.vstack([features_np, synthetic_features])
                        labels = np.concatenate([labels, synthetic_labels])
                    else:
                        features_np = np.vstack([features_np, synthetic_features])
                except Exception as e:
                    logger.warning(f"GAN生成失败: {str(e)}")
        except Exception as e:
            logger.warning(f"数据增强失败: {str(e)}")
            # 如果增强失败，返回原始数据
            if labels is not None:
                return features, labels
            else:
                return features
        
        # 转换回原始类型
        if is_tensor:
            features_result = torch.from_numpy(features_np)
            if device is not None:
                features_result = features_result.to(device)
        else:
            features_result = features_np
        
        if labels is not None:
            return features_result, labels
        else:
            return features_result
    
    def train_gan(self, dataloader, **kwargs):
        """
        训练GAN模型
        
        Args:
            dataloader: 数据加载器
            **kwargs: 其他参数传递给GAN的train_gan方法
        """
        if self.gan is None:
            try:
                self.gan = FlowGAN(
                    feature_dim=self.config.get('feature_dim', 64),
                    latent_dim=self.config.get('latent_dim', 100),
                    hidden_dim=self.config.get('hidden_dim', 128)
                )
            except Exception as e:
                logger.error(f"GAN初始化失败: {str(e)}")
                return
        
        try:
            self.gan.train_gan(dataloader, **kwargs)
            logger.info("GAN模型训练完成")
        except Exception as e:
            logger.error(f"GAN训练失败: {str(e)}")
