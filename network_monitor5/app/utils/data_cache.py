"""
数据缓存模块 - 用于存储和管理增量学习的训练样本

包含功能:
- 样本存储
- 批量获取
- 持久化
- 样本去重
"""

import os
import json
import pickle
import numpy as np
import torch
import logging
import time
from datetime import datetime
from collections import deque

logger = logging.getLogger(__name__)

class DataCache:
    """数据缓存类，用于存储增量学习的训练样本"""
    
    def __init__(self, cache_dir='data_cache', max_samples=10000, batch_size=32):
        """
        初始化数据缓存
        
        Args:
            cache_dir: 缓存目录
            max_samples: 最大样本数量
            batch_size: 批处理大小
        """
        self.cache_dir = cache_dir
        self.max_samples = max_samples
        self.batch_size = batch_size
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        
        # 初始化缓存
        self.features_cache = deque(maxlen=max_samples)
        self.labels_cache = deque(maxlen=max_samples)
        self.metadata_cache = deque(maxlen=max_samples)
        
        # 加载现有缓存
        self._load_cache()
        
        logger.info(f"数据缓存初始化完成，当前样本数: {len(self.features_cache)}")
    
    def add_sample(self, features, label, metadata=None):
        """
        添加样本到缓存
        
        Args:
            features: 特征向量
            label: 标签
            metadata: 元数据（可选）
        
        Returns:
            添加是否成功
        """
        try:
            # 转换为numpy数组
            if isinstance(features, torch.Tensor):
                features = features.cpu().detach().numpy()
            
            # 添加到缓存
            self.features_cache.append(features)
            self.labels_cache.append(label)
            self.metadata_cache.append(metadata or {
                'timestamp': datetime.now().isoformat(),
                'source': 'unknown'
            })
            
            # 如果缓存达到一定大小，自动保存
            if len(self.features_cache) % 100 == 0:
                self._save_cache()
                
            return True
        except Exception as e:
            logger.error(f"添加样本到缓存失败: {str(e)}")
            return False
    
    def add_batch(self, features_batch, labels_batch, metadata_batch=None):
        """
        批量添加样本到缓存
        
        Args:
            features_batch: 特征向量批次
            labels_batch: 标签批次
            metadata_batch: 元数据批次（可选）
        
        Returns:
            添加成功的样本数
        """
        success_count = 0
        
        if metadata_batch is None:
            metadata_batch = [None] * len(features_batch)
        
        for i, (features, label) in enumerate(zip(features_batch, labels_batch)):
            if self.add_sample(features, label, metadata_batch[i]):
                success_count += 1
        
        return success_count
    
    def get_batch(self, batch_size=None):
        """
        获取一批样本
        
        Args:
            batch_size: 批处理大小，如果为None则使用默认值
        
        Returns:
            特征向量批次，标签批次
        """
        if batch_size is None:
            batch_size = self.batch_size
        
        if len(self.features_cache) == 0:
            return None, None
        
        # 随机选择索引
        indices = np.random.choice(
            len(self.features_cache),
            min(batch_size, len(self.features_cache)),
            replace=False
        )
        
        # 获取样本
        features_batch = [self.features_cache[i] for i in indices]
        labels_batch = [self.labels_cache[i] for i in indices]
        
        # 转换为numpy数组
        features_batch = np.array(features_batch)
        labels_batch = np.array(labels_batch)
        
        return features_batch, labels_batch
    
    def get_all_samples(self):
        """
        获取所有样本
        
        Returns:
            所有特征向量，所有标签
        """
        if len(self.features_cache) == 0:
            return None, None
        
        # 转换为numpy数组
        features = np.array(list(self.features_cache))
        labels = np.array(list(self.labels_cache))
        
        return features, labels
    
    def clear_cache(self):
        """清空缓存"""
        self.features_cache.clear()
        self.labels_cache.clear()
        self.metadata_cache.clear()
        
        # 删除缓存文件
        for filename in ['features.pkl', 'labels.pkl', 'metadata.json']:
            filepath = os.path.join(self.cache_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
        
        logger.info("数据缓存已清空")
    
    def _save_cache(self):
        """保存缓存到磁盘"""
        try:
            # 保存特征向量
            with open(os.path.join(self.cache_dir, 'features.pkl'), 'wb') as f:
                pickle.dump(list(self.features_cache), f)
            
            # 保存标签
            with open(os.path.join(self.cache_dir, 'labels.pkl'), 'wb') as f:
                pickle.dump(list(self.labels_cache), f)
            
            # 保存元数据
            with open(os.path.join(self.cache_dir, 'metadata.json'), 'w') as f:
                json.dump(list(self.metadata_cache), f)
            
            logger.info(f"数据缓存已保存，样本数: {len(self.features_cache)}")
            return True
        except Exception as e:
            logger.error(f"保存数据缓存失败: {str(e)}")
            return False
    
    def _load_cache(self):
        """从磁盘加载缓存"""
        try:
            # 加载特征向量
            features_path = os.path.join(self.cache_dir, 'features.pkl')
            if os.path.exists(features_path):
                with open(features_path, 'rb') as f:
                    self.features_cache = deque(pickle.load(f), maxlen=self.max_samples)
            
            # 加载标签
            labels_path = os.path.join(self.cache_dir, 'labels.pkl')
            if os.path.exists(labels_path):
                with open(labels_path, 'rb') as f:
                    self.labels_cache = deque(pickle.load(f), maxlen=self.max_samples)
            
            # 加载元数据
            metadata_path = os.path.join(self.cache_dir, 'metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    self.metadata_cache = deque(json.load(f), maxlen=self.max_samples)
            
            logger.info(f"数据缓存已加载，样本数: {len(self.features_cache)}")
            return True
        except Exception as e:
            logger.error(f"加载数据缓存失败: {str(e)}")
            self.features_cache = deque(maxlen=self.max_samples)
            self.labels_cache = deque(maxlen=self.max_samples)
            self.metadata_cache = deque(maxlen=self.max_samples)
            return False
    
    def get_stats(self):
        """
        获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        # 计算标签分布
        label_counts = {}
        for label in self.labels_cache:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        return {
            'total_samples': len(self.features_cache),
            'label_distribution': label_counts,
            'last_updated': datetime.now().isoformat()
        }


# 创建全局数据缓存实例
anomaly_data_cache = DataCache(cache_dir='data_cache/anomaly')
encryption_data_cache = DataCache(cache_dir='data_cache/encryption')
