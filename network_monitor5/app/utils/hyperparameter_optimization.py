"""
超参数优化模块 - 提供自动化超参数搜索功能

包含功能:
- 贝叶斯优化
- 网格搜索
- 随机搜索
- 超参数评估
"""

import numpy as np
import torch
import logging
import time
import json
import os
from datetime import datetime
from functools import partial
from sklearn.model_selection import KFold, train_test_split

# 尝试导入可选依赖
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    logging.warning("scikit-optimize未安装，贝叶斯优化功能将不可用")

logger = logging.getLogger(__name__)

class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, model_class, data_loader, val_loader=None, method='bayesian', 
                 n_trials=50, cv_folds=3, metric='accuracy', device='cpu', save_dir='hyperopt_results'):
        """
        初始化超参数优化器
        
        Args:
            model_class: 模型类
            data_loader: 训练数据加载器
            val_loader: 验证数据加载器（可选）
            method: 优化方法，可选 'bayesian', 'grid', 'random'
            n_trials: 试验次数
            cv_folds: 交叉验证折数
            metric: 评估指标，可选 'accuracy', 'f1', 'auc', 'loss'
            device: 设备
            save_dir: 结果保存目录
        """
        self.model_class = model_class
        self.data_loader = data_loader
        self.val_loader = val_loader
        self.method = method
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.metric = metric
        self.device = device
        self.save_dir = save_dir
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 检查贝叶斯优化是否可用
        if method == 'bayesian' and not SKOPT_AVAILABLE:
            logger.warning("贝叶斯优化不可用，将使用随机搜索代替")
            self.method = 'random'
    
    def optimize(self, param_space, fixed_params=None, epochs=10, batch_size=32):
        """
        执行超参数优化
        
        Args:
            param_space: 参数空间定义
            fixed_params: 固定参数
            epochs: 每次试验的训练轮数
            batch_size: 批量大小
            
        Returns:
            最佳参数和评估结果
        """
        fixed_params = fixed_params or {}
        start_time = time.time()
        
        if self.method == 'bayesian':
            best_params, best_score = self._bayesian_optimization(param_space, fixed_params, epochs, batch_size)
        elif self.method == 'grid':
            best_params, best_score = self._grid_search(param_space, fixed_params, epochs, batch_size)
        elif self.method == 'random':
            best_params, best_score = self._random_search(param_space, fixed_params, epochs, batch_size)
        else:
            raise ValueError(f"不支持的优化方法: {self.method}")
        
        # 记录结果
        elapsed_time = time.time() - start_time
        result = {
            'best_params': best_params,
            'best_score': best_score,
            'method': self.method,
            'n_trials': self.n_trials,
            'metric': self.metric,
            'elapsed_time': elapsed_time,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存结果
        result_file = os.path.join(self.save_dir, f"hyperopt_result_{int(time.time())}.json")
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=4)
        
        logger.info(f"超参数优化完成，最佳参数: {best_params}, 最佳得分: {best_score:.4f}, 用时: {elapsed_time:.2f}秒")
        return best_params, best_score
    
    def _bayesian_optimization(self, param_space, fixed_params, epochs, batch_size):
        """贝叶斯优化"""
        # 转换参数空间为skopt格式
        dimensions = []
        param_names = []
        
        for name, space in param_space.items():
            param_names.append(name)
            if isinstance(space, list):
                if all(isinstance(x, int) for x in space):
                    dimensions.append(Integer(min(space), max(space), name=name))
                elif all(isinstance(x, float) for x in space):
                    dimensions.append(Real(min(space), max(space), name=name))
                else:
                    dimensions.append(Categorical(space, name=name))
            elif isinstance(space, tuple) and len(space) == 2:
                if isinstance(space[0], int) and isinstance(space[1], int):
                    dimensions.append(Integer(space[0], space[1], name=name))
                else:
                    dimensions.append(Real(space[0], space[1], name=name))
            else:
                dimensions.append(Categorical(space, name=name))
        
        # 定义目标函数
        @use_named_args(dimensions=dimensions)
        def objective(**params):
            # 合并固定参数
            all_params = {**params, **fixed_params}
            
            # 评估模型
            score = self._evaluate_model(all_params, epochs, batch_size)
            
            # 贝叶斯优化是最小化问题，所以对于准确率等指标，需要取负值
            if self.metric in ['accuracy', 'f1', 'auc']:
                return -score
            else:
                return score
        
        # 执行贝叶斯优化
        result = gp_minimize(
            func=objective,
            dimensions=dimensions,
            n_calls=self.n_trials,
            random_state=42,
            verbose=True
        )
        
        # 提取最佳参数
        best_params = {param_names[i]: result.x[i] for i in range(len(param_names))}
        best_score = -result.fun if self.metric in ['accuracy', 'f1', 'auc'] else result.fun
        
        return best_params, best_score
    
    def _grid_search(self, param_space, fixed_params, epochs, batch_size):
        """网格搜索"""
        # 生成网格点
        param_grid = []
        param_names = list(param_space.keys())
        param_values = list(param_space.values())
        
        # 转换参数空间为列表
        param_lists = []
        for values in param_values:
            if isinstance(values, list):
                param_lists.append(values)
            elif isinstance(values, tuple) and len(values) == 2:
                # 对于范围，生成线性间隔的点
                if isinstance(values[0], int) and isinstance(values[1], int):
                    # 整数范围
                    num_points = min(10, values[1] - values[0] + 1)  # 最多10个点
                    param_lists.append(np.linspace(values[0], values[1], num_points, dtype=int).tolist())
                else:
                    # 浮点数范围
                    param_lists.append(np.linspace(values[0], values[1], 10).tolist())
            else:
                param_lists.append([values])
        
        # 生成所有组合
        import itertools
        param_combinations = list(itertools.product(*param_lists))
        
        # 限制组合数量
        if len(param_combinations) > self.n_trials:
            logger.warning(f"网格搜索组合数 ({len(param_combinations)}) 超过了试验次数限制 ({self.n_trials})，将随机采样")
            indices = np.random.choice(len(param_combinations), self.n_trials, replace=False)
            param_combinations = [param_combinations[i] for i in indices]
        
        # 评估每个组合
        best_score = float('-inf') if self.metric in ['accuracy', 'f1', 'auc'] else float('inf')
        best_params = None
        
        for i, combination in enumerate(param_combinations):
            params = {param_names[j]: combination[j] for j in range(len(param_names))}
            params.update(fixed_params)
            
            logger.info(f"评估参数组合 {i+1}/{len(param_combinations)}: {params}")
            score = self._evaluate_model(params, epochs, batch_size)
            
            # 更新最佳结果
            if (self.metric in ['accuracy', 'f1', 'auc'] and score > best_score) or \
               (self.metric not in ['accuracy', 'f1', 'auc'] and score < best_score):
                best_score = score
                best_params = params
        
        return best_params, best_score
    
    def _random_search(self, param_space, fixed_params, epochs, batch_size):
        """随机搜索"""
        best_score = float('-inf') if self.metric in ['accuracy', 'f1', 'auc'] else float('inf')
        best_params = None
        
        for i in range(self.n_trials):
            # 随机采样参数
            params = {}
            for name, space in param_space.items():
                if isinstance(space, list):
                    params[name] = np.random.choice(space)
                elif isinstance(space, tuple) and len(space) == 2:
                    if isinstance(space[0], int) and isinstance(space[1], int):
                        params[name] = np.random.randint(space[0], space[1] + 1)
                    else:
                        params[name] = np.random.uniform(space[0], space[1])
                else:
                    params[name] = space
            
            # 合并固定参数
            params.update(fixed_params)
            
            logger.info(f"评估参数组合 {i+1}/{self.n_trials}: {params}")
            score = self._evaluate_model(params, epochs, batch_size)
            
            # 更新最佳结果
            if (self.metric in ['accuracy', 'f1', 'auc'] and score > best_score) or \
               (self.metric not in ['accuracy', 'f1', 'auc'] and score < best_score):
                best_score = score
                best_params = params
        
        return best_params, best_score
    
    def _evaluate_model(self, params, epochs, batch_size):
        """评估模型性能"""
        if self.val_loader is not None:
            # 使用验证集评估
            return self._evaluate_with_val_loader(params, epochs, batch_size)
        else:
            # 使用交叉验证评估
            return self._evaluate_with_cross_validation(params, epochs, batch_size)
    
    def _evaluate_with_val_loader(self, params, epochs, batch_size):
        """使用验证集评估模型"""
        # 创建模型
        model = self.model_class(**params)
        model.to(self.device)
        
        # 训练模型
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=params.get('lr', 0.001))
        
        for epoch in range(epochs):
            model.train()
            for batch_x, batch_y in self.data_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
        
        # 评估模型
        model.eval()
        correct = 0
        total = 0
        val_loss = 0
        
        with torch.no_grad():
            for batch_x, batch_y in self.val_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
                
                _, predicted = torch.max(outputs.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()
        
        # 计算评估指标
        if self.metric == 'accuracy':
            return correct / total
        elif self.metric == 'loss':
            return val_loss / len(self.val_loader)
        else:
            # 其他指标需要额外计算
            raise NotImplementedError(f"暂不支持的评估指标: {self.metric}")
    
    def _evaluate_with_cross_validation(self, params, epochs, batch_size):
        """使用交叉验证评估模型"""
        # 收集所有数据
        all_data = []
        all_labels = []
        
        for batch_x, batch_y in self.data_loader:
            all_data.append(batch_x)
            all_labels.append(batch_y)
        
        all_data = torch.cat(all_data, dim=0)
        all_labels = torch.cat(all_labels, dim=0)
        
        # 交叉验证
        kf = KFold(n_splits=self.cv_folds, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in kf.split(range(len(all_data))):
            # 分割数据
            train_x, train_y = all_data[train_idx], all_labels[train_idx]
            val_x, val_y = all_data[val_idx], all_labels[val_idx]
            
            # 创建数据加载器
            from torch.utils.data import TensorDataset, DataLoader
            train_dataset = TensorDataset(train_x, train_y)
            val_dataset = TensorDataset(val_x, val_y)
            
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size)
            
            # 创建模型
            model = self.model_class(**params)
            model.to(self.device)
            
            # 训练模型
            criterion = torch.nn.CrossEntropyLoss()
            optimizer = torch.optim.Adam(model.parameters(), lr=params.get('lr', 0.001))
            
            for epoch in range(epochs):
                model.train()
                for batch_x, batch_y in train_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
            
            # 评估模型
            model.eval()
            correct = 0
            total = 0
            val_loss = 0
            
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
                    
                    _, predicted = torch.max(outputs.data, 1)
                    total += batch_y.size(0)
                    correct += (predicted == batch_y).sum().item()
            
            # 计算评估指标
            if self.metric == 'accuracy':
                cv_scores.append(correct / total)
            elif self.metric == 'loss':
                cv_scores.append(val_loss / len(val_loader))
            else:
                # 其他指标需要额外计算
                raise NotImplementedError(f"暂不支持的评估指标: {self.metric}")
        
        # 返回平均得分
        return np.mean(cv_scores)


def optimize_model_hyperparameters(model_class, data_loader, val_loader=None, param_space=None, 
                                  fixed_params=None, method='bayesian', n_trials=30, 
                                  metric='accuracy', device='cpu'):
    """
    优化模型超参数的便捷函数
    
    Args:
        model_class: 模型类
        data_loader: 训练数据加载器
        val_loader: 验证数据加载器（可选）
        param_space: 参数空间定义
        fixed_params: 固定参数
        method: 优化方法，可选 'bayesian', 'grid', 'random'
        n_trials: 试验次数
        metric: 评估指标，可选 'accuracy', 'f1', 'auc', 'loss'
        device: 设备
        
    Returns:
        最佳参数和评估结果
    """
    # 默认参数空间
    if param_space is None:
        param_space = {
            'hidden_size': [64, 128, 256],
            'num_layers': [1, 2, 3],
            'dropout': (0.1, 0.7),
            'lr': (0.0001, 0.01)
        }
    
    # 创建优化器
    optimizer = HyperparameterOptimizer(
        model_class=model_class,
        data_loader=data_loader,
        val_loader=val_loader,
        method=method,
        n_trials=n_trials,
        metric=metric,
        device=device
    )
    
    # 执行优化
    return optimizer.optimize(param_space, fixed_params)
