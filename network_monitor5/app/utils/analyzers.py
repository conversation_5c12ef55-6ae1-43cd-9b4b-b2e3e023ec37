import os
import json
from datetime import datetime
from scapy.all import rdpcap, IP, TCP, UDP
try:
    from scapy.layers.tls import TLS
except ImportError:
    # 如果TLS模块不可用，创建一个占位符
    class TLSPlaceholder:
        pass
    TLS = TLSPlaceholder

from collections import defaultdict
from .deep_learning_model import NetworkTrafficAnalyzer
try:
    from .encryption_analyzer import EncryptionAnalyzer, JA3Calculator, CertificateAnalyzer, ALPNAnalyzer
    ENCRYPTION_ANALYZER_AVAILABLE = True
except ImportError:
    ENCRYPTION_ANALYZER_AVAILABLE = False

# 注意：以下类仅用于提供额外的分析功能，不应与 pcap_analyzer.py 和 flow_analyzer.py 中的类混淆
# 在实际使用中，应优先使用 pcap_analyzer.py 和 flow_analyzer.py 中定义的分析器类

class BasicPcapAnalyzer:
    """基础PCAP文件分析器，用于提供补充功能"""
    def __init__(self, model_type='advanced'):
        self.dl_model = NetworkTrafficAnalyzer(model_type=model_type)

    def extract_basic_info(self, packet, packet_number):
        """提取数据包基本信息"""
        info = {
            'number': packet_number,
            'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
            'length': len(packet),
            'protocol': 'Unknown',
            'src': '',
            'dst': '',
            'layers': {}
        }

        # 提取IP层信息
        if IP in packet:
            info['src'] = packet[IP].src
            info['dst'] = packet[IP].dst
            info['protocol'] = packet[IP].proto
            # 处理IP标志位 - 确保FlagValue类型被正确转换为数值
            try:
                ip_flags = int(packet[IP].flags)
            except (TypeError, ValueError):
                # 如果直接转换失败，尝试获取flags的值
                ip_flags = packet[IP].flags.value if hasattr(packet[IP].flags, 'value') else 0

            info['layers']['ip'] = {
                'version': packet[IP].version,
                'ihl': packet[IP].ihl,
                'tos': packet[IP].tos,
                'len': packet[IP].len,
                'id': packet[IP].id,
                'flags': ip_flags,  # 使用转换后的数值
                'frag': packet[IP].frag,
                'ttl': packet[IP].ttl,
                'proto': packet[IP].proto
            }

        # 提取TCP/UDP层信息
        if TCP in packet:
            info['protocol'] = 'TCP'

            # 处理TCP标志位 - 确保FlagValue类型被正确转换为数值
            try:
                tcp_flags = int(packet[TCP].flags)
            except (TypeError, ValueError):
                # 如果直接转换失败，尝试获取flags的值
                tcp_flags = packet[TCP].flags.value if hasattr(packet[TCP].flags, 'value') else 0

            info['layers']['tcp'] = {
                'sport': packet[TCP].sport,
                'dport': packet[TCP].dport,
                'seq': packet[TCP].seq,
                'ack': packet[TCP].ack,
                'flags': tcp_flags  # 使用转换后的数值
            }
        elif UDP in packet:
            info['protocol'] = 'UDP'
            info['layers']['udp'] = {
                'sport': packet[UDP].sport,
                'dport': packet[UDP].dport,
                'len': packet[UDP].len
            }

        return info

class EnhancedEncryptionDetector:
    """增强的加密流量检测器，提供额外的加密检测功能"""
    def __init__(self, model_type='advanced', model_path=None):
        """
        初始化加密流量检测器

        Args:
            model_type: 模型类型，可选 'simple', 'cnnlstm', 'advanced'
            model_path: 模型文件路径，如果提供则加载模型
        """
        # 初始化深度学习模型，默认使用高级模型
        self.dl_model = NetworkTrafficAnalyzer(model_type=model_type, model_path=model_path)
        print(f"使用{model_type}模型进行加密流量检测")

        # 已知加密协议端口
        self.encryption_protocols = {
            443: 'HTTPS',
            22: 'SSH',
            989: 'FTPS',
            990: 'FTPS',
            993: 'IMAPS',
            995: 'POP3S',
            8443: 'HTTPS-Alt',
            465: 'SMTPS',
            636: 'LDAPS',
            5061: 'SIP-TLS'
        }

        # 置信度阈值
        self.confidence_threshold = 0.7

        # 初始化高级加密分析器（如果可用）
        self.advanced_analyzer = None
        if ENCRYPTION_ANALYZER_AVAILABLE:
            try:
                self.advanced_analyzer = EncryptionAnalyzer()
                print("高级加密分析器初始化成功")
            except Exception as e:
                print(f"高级加密分析器初始化失败: {str(e)}")

    def is_tls_packet(self, packet):
        """检测是否为TLS数据包"""
        try:
            # 检查TLS是否为占位符类
            if isinstance(TLS, type) and TLS.__name__ == 'TLSPlaceholder':
                # TLS模块不可用，尝试使用端口号进行简单检测
                if TCP in packet:
                    dport = packet[TCP].dport
                    sport = packet[TCP].sport
                    return dport == 443 or sport == 443 or dport == 8443 or sport == 8443
                return False
            else:
                # TLS模块可用，正常检测
                return TLS in packet
        except Exception as e:
            print(f"TLS检测错误: {str(e)}")
            return False

    def detect_encryption_in_packet(self, packet):
        """检测数据包是否加密"""
        # 使用高级加密分析器（如果可用）
        if self.advanced_analyzer and ENCRYPTION_ANALYZER_AVAILABLE:
            try:
                # 检查TLS模块是否可用
                if not isinstance(TLS, type) or TLS.__name__ != 'TLSPlaceholder':
                    result = self.advanced_analyzer.analyze_packet(packet)
                    if result["is_encrypted"]:
                        return True, result["encryption_type"]
            except Exception as e:
                print(f"高级加密分析失败: {str(e)}")

        # 基于协议的检测
        if self.is_tls_packet(packet):
            return True, 'TLS/SSL'

        if TCP in packet:
            dport = packet[TCP].dport
            sport = packet[TCP].sport
            if dport in self.encryption_protocols:
                return True, self.encryption_protocols[dport]
            if sport in self.encryption_protocols:
                return True, self.encryption_protocols[sport]

        # 基于深度学习的检测
        try:
            # 使用带置信度的预测
            prediction, confidence = self.dl_model.predict_with_confidence(packet)
            if prediction == 1 and confidence >= self.confidence_threshold:  # 1表示加密
                return True, f'ML-Detected_{confidence:.2f}'
        except Exception as e:
            print(f"深度学习加密检测错误: {str(e)}")

        return False, None

    def analyze_file_for_encryption(self, pcap_file):
        """分析PCAP文件中的加密流量"""
        try:
            # 使用高级加密分析器（如果可用）
            if self.advanced_analyzer and ENCRYPTION_ANALYZER_AVAILABLE and (not isinstance(TLS, type) or TLS.__name__ != 'TLSPlaceholder'):
                try:
                    advanced_result = self.advanced_analyzer.analyze_pcap(pcap_file)
                    if 'error' not in advanced_result:
                        print(f"使用高级加密分析器分析完成，共有 {advanced_result['total_packets']} 个数据包，其中加密数据包 {advanced_result['encrypted_packets']} 个")

                        # 添加高级分析结果
                        advanced_result['advanced_analysis'] = {
                            'ja3_fingerprints': advanced_result.get('ja3_fingerprints', {}),
                            'certificate_issues': advanced_result.get('certificate_issues', []),
                            'alpn_protocols': advanced_result.get('alpn_protocols', {}),
                            'tls_versions': advanced_result.get('tls_versions', {})
                        }

                        # 将高级分析结果转换为标准格式
                        result = {
                            'packets': [],
                            'protocol_stats': defaultdict(int),
                            'time_series': [],
                            'ml_detected_count': 0,
                            'rule_detected_count': 0,
                            'advanced_analysis': advanced_result['advanced_analysis']
                        }

                        # 处理详细结果
                        for packet_result in advanced_result.get('detailed_results', []):
                            packet_info = {
                                'number': packet_result['packet_number'],
                                'is_encrypted': packet_result['result']['is_encrypted'],
                                'encryption_type': packet_result['result']['encryption_type']
                            }

                            # 添加高级特征
                            if 'features' in packet_result['result']:
                                features = packet_result['result']['features']

                                # 添加JA3指纹
                                if 'ja3_hash' in features:
                                    packet_info['ja3_hash'] = features['ja3_hash']
                                    if 'client_info' in features:
                                        packet_info['client_info'] = features['client_info']

                                # 添加证书信息
                                if 'certificate' in features:
                                    cert_info = features['certificate']
                                    packet_info['certificate'] = {
                                        'issuer': cert_info.get('issuer'),
                                        'subject': cert_info.get('subject'),
                                        'valid_to': cert_info.get('valid_to'),
                                        'is_expired': cert_info.get('is_expired'),
                                        'is_self_signed': cert_info.get('is_self_signed')
                                    }

                                # 添加ALPN协议
                                if 'alpn' in features:
                                    packet_info['alpn'] = features['alpn']

                            result['packets'].append(packet_info)

                            # 更新协议统计
                            if packet_info['is_encrypted'] and packet_info['encryption_type']:
                                result['protocol_stats'][packet_info['encryption_type']] += 1

                        # 添加总体统计信息
                        result['summary'] = {
                            'total_packets': advanced_result['total_packets'],
                            'encrypted_packets': advanced_result['encrypted_packets'],
                            'encryption_ratio': advanced_result['encryption_ratio'] * 100,
                            'ja3_fingerprint_count': len(advanced_result.get('ja3_fingerprints', {})),
                            'certificate_issues_count': len(advanced_result.get('certificate_issues', [])),
                            'alpn_protocol_count': len(advanced_result.get('alpn_protocols', {})),
                            'entropy_data': advanced_result.get('entropy_data', []),
                            'entropy_time_series': advanced_result.get('entropy_time_series', [])
                        }

                        return result
                except Exception as e:
                    print(f"高级加密分析失败，切换到标准分析: {str(e)}")

            # 标准分析方法
            packets = rdpcap(pcap_file)
            result = {
                'packets': [],
                'protocol_stats': defaultdict(int),
                'time_series': [],
                'ml_detected_count': 0,
                'rule_detected_count': 0,
                'entropy_data': [],
                'entropy_time_series': []
            }

            window_size = 100  # 时间序列窗口大小
            window_packets = []

            for i, packet in enumerate(packets, 1):
                analyzer = BasicPcapAnalyzer(model_type=self.dl_model.model_type)
                packet_info = analyzer.extract_basic_info(packet, i)
                is_encrypted, encryption_type = self.detect_encryption_in_packet(packet)

                packet_info['is_encrypted'] = is_encrypted
                packet_info['encryption_type'] = encryption_type

                # 计算熵值
                entropy = None
                packet_size = len(packet) if hasattr(packet, '__len__') else 0

                if hasattr(packet, 'payload') and hasattr(packet.payload, 'load'):
                    try:
                        payload = bytes(packet.payload.load)
                        if payload:
                            # 使用高级加密分析器的特征提取器计算熵值
                            if self.advanced_analyzer and hasattr(self.advanced_analyzer, 'feature_extractor'):
                                entropy = self.advanced_analyzer.feature_extractor.calculate_entropy(payload)
                            else:
                                # 简单的熵值计算
                                _, counts = np.unique(np.frombuffer(payload, dtype=np.uint8), return_counts=True)
                                entropy = -np.sum((counts / len(payload)) * np.log2(counts / len(payload)))
                    except Exception as e:
                        print(f"计算数据包 {i} 的熵值时出错: {str(e)}")

                # 如果计算了熵值，添加到结果中
                if entropy is not None:
                    result['entropy_data'].append({
                        'packet_number': i,
                        'entropy': entropy,
                        'is_encrypted': is_encrypted,
                        'size': packet_size
                    })

                    # 添加到时间序列
                    timestamp = packet.time if hasattr(packet, 'time') else i
                    result['entropy_time_series'].append({
                        'timestamp': timestamp,
                        'entropy': entropy,
                        'is_encrypted': is_encrypted
                    })

                if is_encrypted and encryption_type:
                    result['protocol_stats'][encryption_type] += 1
                    # 统计深度学习检测和规则检测的数量
                    if 'ML-Detected' in str(encryption_type):
                        result['ml_detected_count'] += 1
                    else:
                        result['rule_detected_count'] += 1

                result['packets'].append(packet_info)
                window_packets.append(is_encrypted)

                # 计算时间序列数据
                if len(window_packets) >= window_size:
                    encrypted_ratio = (sum(window_packets) / window_size) * 100
                    result['time_series'].append({
                        'time': packet_info['time'],
                        'encryption_ratio': encrypted_ratio
                    })
                    window_packets = window_packets[1:]

            # 添加总体统计信息
            total_packets = len(packets)
            encrypted_packets = sum(1 for p in result['packets'] if p['is_encrypted'])
            result['summary'] = {
                'total_packets': total_packets,
                'encrypted_packets': encrypted_packets,
                'encryption_ratio': (encrypted_packets / total_packets * 100) if total_packets > 0 else 0,
                'ml_detection_ratio': (result['ml_detected_count'] / encrypted_packets * 100) if encrypted_packets > 0 else 0,
                'entropy_data': result['entropy_data'],
                'entropy_time_series': result['entropy_time_series']
            }

            return result
        except Exception as e:
            print(f"加密分析错误: {str(e)}")
            return {
                'packets': [],
                'protocol_stats': {},
                'time_series': [],
                'ml_detected_count': 0,
                'rule_detected_count': 0,
                'summary': {
                    'total_packets': 0,
                    'encrypted_packets': 0,
                    'encryption_ratio': 0,
                    'ml_detection_ratio': 0
                }
            }

class AdvancedAnomalyDetector:
    """高级异常流量检测器，提供更复杂的异常检测功能"""
    def __init__(self, model_type='advanced', model_path=None):
        """
        初始化高级异常检测器

        Args:
            model_type: 模型类型，可选 'simple', 'cnnlstm', 'advanced'
            model_path: 模型文件路径，如果提供则加载模型
        """
        # 初始化深度学习模型，默认使用高级模型
        self.dl_model = NetworkTrafficAnalyzer(model_type=model_type, model_path=model_path)
        print(f"使用{model_type}模型进行异常检测")

        # 基于规则的检测签名
        self.attack_signatures = {
            'syn_flood': lambda p: TCP in p and p[TCP].flags == 2,  # SYN flag only
            'port_scan': lambda p: TCP in p and p[TCP].flags == 2 and p[TCP].dport in range(1, 1024),
            'ping_flood': lambda p: p.haslayer('ICMP'),
            'udp_flood': lambda p: UDP in p and len(p) >= 1000
        }

        # 置信度阈值
        self.confidence_threshold = 0.75

    def detect_anomaly_in_packet(self, packet):
        """检测数据包是否异常"""
        # 首先基于规则进行检测
        for attack_type, signature in self.attack_signatures.items():
            try:
                if signature(packet):
                    return True, attack_type
            except:
                continue

        # 然后使用深度学习模型进行检测
        try:
            # 使用带置信度的预测
            prediction, confidence = self.dl_model.predict_with_confidence(packet)
            if prediction == 1 and confidence >= self.confidence_threshold:  # 1表示异常
                return True, f'ml_detected_{confidence:.2f}'
        except Exception as e:
            print(f"深度学习检测错误: {str(e)}")

        return False, None

    def analyze_pcap_file(self, pcap_file):
        """分析PCAP文件中的异常流量"""
        try:
            packets = rdpcap(pcap_file)
            result = {
                'packets': [],
                'anomaly_stats': defaultdict(int),
                'time_series': []
            }

            window_size = 100  # 时间序列窗口大小
            window_anomalies = []

            for i, packet in enumerate(packets, 1):
                analyzer = BasicPcapAnalyzer(model_type=self.dl_model.model_type)
                packet_info = analyzer.extract_basic_info(packet, i)
                is_anomaly, attack_type = self.detect_anomaly_in_packet(packet)

                packet_info['is_anomaly'] = is_anomaly
                packet_info['attack_type'] = attack_type

                if is_anomaly and attack_type:
                    result['anomaly_stats'][attack_type] += 1

                result['packets'].append(packet_info)
                window_anomalies.append(is_anomaly)

                # 计算时间序列数据
                if len(window_anomalies) >= window_size:
                    anomaly_ratio = (sum(window_anomalies) / window_size) * 100
                    result['time_series'].append({
                        'time': packet_info['time'],
                        'anomaly_ratio': anomaly_ratio
                    })
                    window_anomalies = window_anomalies[1:]

            return result
        except Exception as e:
            print(f"异常检测分析错误: {str(e)}")
            return {
                'packets': [],
                'anomaly_stats': {},
                'time_series': []
            }