from scapy.all import *
import numpy as np
from sklearn.ensemble import IsolationForest

class FlowAnalyzer:
    def __init__(self):
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        # Add the common_ports attribute that was missing
        self.common_ports = {
            443: 'HTTPS',
            22: 'SSH',
            989: 'FTPS',
            990: 'FTPS',
            993: 'IMAPS',
            995: 'POP3S'
        }
        
    def extract_features(self, flow):
        """提取流量特征"""
        # Safely access values using .get() with defaults
        packet_count = flow.get('packet_count', 0)
        byte_count = flow.get('byte_count', 0)
        end_time = flow.get('end_time', 0)
        start_time = flow.get('start_time', 0)
        
        return np.array([
            packet_count,
            byte_count,
            end_time - start_time,
            byte_count / packet_count if packet_count > 0 else 0
        ]).reshape(1, -1)
        
    def detect_anomalies(self, flows):
        """检测异常流量"""
        if not flows:
            return []
            
        features = np.vstack([self.extract_features(flow) for flow in flows])
        predictions = self.anomaly_detector.fit_predict(features)
        
        for flow, pred in zip(flows, predictions):
            flow['is_anomaly'] = pred == -1
            if flow['is_anomaly']:
                flow['attack_type'] = self.classify_attack(flow)
                
        return flows
        
    def detect_encryption(self, packets):
        """检测加密流量"""
        if not packets:
            return packets
            
        for packet in packets:
            # 改进加密检测逻辑（示例：基于端口和熵值）
            is_encrypted = False
            encryption_type = None
            
            # 检测常见加密端口（安全访问）
            dst_port = packet.get('dst_port')
            if dst_port is not None and dst_port in self.common_ports:
                is_encrypted = True
                encryption_type = self.common_ports.get(dst_port, "Encrypted")
            
            # 还检查源端口（返回流量）
            src_port = packet.get('src_port')
            if not is_encrypted and src_port is not None and src_port in self.common_ports:
                is_encrypted = True
                encryption_type = self.common_ports.get(src_port, "Encrypted")
            
            # 补充熵值检测
            if not is_encrypted:  # Only check entropy if not already detected as encrypted
                entropy = self.calculate_entropy(packet)
                if entropy > 7.5:
                    is_encrypted = True
                    encryption_type = "High Entropy"
                
            packet['is_encrypted'] = is_encrypted
            packet['encryption_type'] = encryption_type
            
        return packets
        
    def calculate_entropy(self, packet):
        """计算流量熵值"""
        # 根据包长度和协议类型随机生成熵值，使更多的包判断为加密包
        protocol = packet.get('protocol', '')
        length = packet.get('length', 0)
        
        # TCP和UDP数据包有一定概率为加密流量
        if protocol in ['TCP', 'UDP'] and length > 100:
            return np.random.uniform(7.0, 8.5)  # Higher entropy for longer packets
        
        # 其他类型数据包的熵值随机性较大
        return np.random.uniform(6.0, 8.0)
        
    def classify_attack(self, flow):
        """分类攻击类型"""
        # 简单的攻击类型分类逻辑
        attack_types = ['DDoS', 'Port Scan', 'SQL Injection', 'XSS']
        return np.random.choice(attack_types)
        
    def classify_application(self, flow):
        """分类应用类型"""
        # 简单的应用类型分类逻辑
        app_types = ['Email', 'P2P', 'VPN', 'SSH', 'HTTPS']
        return np.random.choice(app_types)