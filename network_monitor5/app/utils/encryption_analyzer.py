"""
加密流量分析模块 - 提供高级加密流量分析功能

包含功能:
- JA3/JA3S 指纹计算
- SSL/TLS 证书分析
- ALPN 协议解析
- 加密流量特征提取
"""

import logging
import hashlib
import datetime
import re
import socket
import struct
import binascii
import os
from collections import defaultdict
import numpy as np

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入可选依赖
try:
    from cryptography import x509
    from cryptography.hazmat.backends import default_backend
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    logger.warning("cryptography 库未安装，证书分析功能将受限")
    CRYPTOGRAPHY_AVAILABLE = False

try:
    from scapy.all import *
    from scapy.layers.tls import *
    TLS_LAYER_AVAILABLE = True
except ImportError:
    logger.warning("scapy TLS 层未安装，TLS 分析功能将受限")
    TLS_LAYER_AVAILABLE = False

# TLS 版本映射
TLS_VERSIONS = {
    0x0300: "SSL 3.0",
    0x0301: "TLS 1.0",
    0x0302: "TLS 1.1",
    0x0303: "TLS 1.2",
    0x0304: "TLS 1.3"
}

# ALPN 协议映射
ALPN_PROTOCOLS = {
    b'h2': 'HTTP/2',
    b'http/1.1': 'HTTP/1.1',
    b'http/1.0': 'HTTP/1.0',
    b'spdy/1': 'SPDY/1',
    b'spdy/2': 'SPDY/2',
    b'spdy/3': 'SPDY/3',
    b'h3': 'HTTP/3',
    b'ftp': 'FTP',
    b'imap': 'IMAP',
    b'pop3': 'POP3',
    b'smtp': 'SMTP',
    b'acme-tls/1': 'ACME TLS/1',
    b'webrtc': 'WebRTC',
    b'stun.turn': 'STUN/TURN',
    b'stun.nat-discovery': 'STUN NAT Discovery'
}

class JA3Calculator:
    """计算 JA3 和 JA3S 指纹"""

    @staticmethod
    def calculate_ja3(packet):
        """
        计算 JA3 指纹 (客户端 Hello)

        JA3 = MD5(TLSVersion,Ciphers,Extensions,EllipticCurves,EllipticCurvePointFormats)

        Args:
            packet: 包含 TLS ClientHello 的数据包

        Returns:
            tuple: (JA3 指纹, JA3 字符串)
        """
        if not TLS_LAYER_AVAILABLE:
            return None, None

        try:
            if not packet.haslayer(TLS) or not packet.haslayer(TLSClientHello):
                return None, None

            client_hello = packet[TLSClientHello]

            # TLS 版本
            tls_version = client_hello.version

            # 密码套件
            ciphers = client_hello.ciphers
            cipher_str = ",".join([str(c) for c in ciphers])

            # 扩展
            extensions = []
            ext_types = []
            curves = []
            point_formats = []

            if hasattr(client_hello, 'ext'):
                for ext in client_hello.ext:
                    ext_type = ext.type
                    ext_types.append(str(ext_type))

                    # 椭圆曲线
                    if ext_type == 10:  # 支持的曲线
                        if hasattr(ext, 'groups'):
                            curves = [str(c) for c in ext.groups]
                        elif hasattr(ext, 'curves'):
                            curves = [str(c) for c in ext.curves]

                    # 椭圆曲线点格式
                    if ext_type == 11:  # 支持的点格式
                        if hasattr(ext, 'ecpl'):
                            point_formats = [str(p) for p in ext.ecpl]

            # 构建 JA3 字符串
            ja3_str = f"{tls_version},{cipher_str},{','.join(ext_types)},{','.join(curves)},{','.join(point_formats)}"
            ja3_hash = hashlib.md5(ja3_str.encode()).hexdigest()

            return ja3_hash, ja3_str

        except Exception as e:
            logger.error(f"计算 JA3 指纹时出错: {str(e)}")
            return None, None

    @staticmethod
    def calculate_ja3s(packet):
        """
        计算 JA3S 指纹 (服务器 Hello)

        JA3S = MD5(TLSVersion,Cipher,Extensions)

        Args:
            packet: 包含 TLS ServerHello 的数据包

        Returns:
            tuple: (JA3S 指纹, JA3S 字符串)
        """
        if not TLS_LAYER_AVAILABLE:
            return None, None

        try:
            if not packet.haslayer(TLS) or not packet.haslayer(TLSServerHello):
                return None, None

            server_hello = packet[TLSServerHello]

            # TLS 版本
            tls_version = server_hello.version

            # 选择的密码套件
            cipher = server_hello.cipher

            # 扩展
            ext_types = []

            if hasattr(server_hello, 'ext'):
                for ext in server_hello.ext:
                    ext_types.append(str(ext.type))

            # 构建 JA3S 字符串
            ja3s_str = f"{tls_version},{cipher},{','.join(ext_types)}"
            ja3s_hash = hashlib.md5(ja3s_str.encode()).hexdigest()

            return ja3s_hash, ja3s_str

        except Exception as e:
            logger.error(f"计算 JA3S 指纹时出错: {str(e)}")
            return None, None

    @staticmethod
    def get_ja3_info(ja3_hash):
        """
        获取 JA3 指纹的相关信息

        Args:
            ja3_hash: JA3 哈希值

        Returns:
            dict: JA3 指纹信息
        """
        # 常见 JA3 指纹映射 (可以扩展)
        ja3_db = {
            "e7d705a3286e19ea42f587b344ee6865": {"client": "Chrome", "version": "74-79"},
            "cd08e31494f9531f560d64c695473da9": {"client": "Chrome", "version": "80+"},
            "a0e9f5d64349fb13191bc781f81f42e1": {"client": "Firefox", "version": "62-64"},
            "9e0a6bb78aaa113a5865ab6c7c0a05b4": {"client": "Edge", "version": "Chromium-based"},
            "4d7a28d6f2263ed61de88ca66eb011e3": {"client": "Safari", "version": "12-13"},
            "6fa3244216a38b9d06526fd1e8b460c5": {"client": "curl", "version": "7.x"},
            "fd2cfd6b9e8f66c6b5688c2f31de6b67": {"client": "OpenSSL", "version": "1.1.x"},
            "200a12a8108c1950a443d7562214ec45": {"client": "Java", "version": "8-11"},
            "51c64c77e60f3980eea90869b68c58a8": {"client": "Python", "version": "3.x"},
            "6baba9afc0d1fca7c88b9f4ef5a9e4b3": {"client": "Malware", "version": "Trickbot"},
            "4d7a28d6f2263ed61de88ca66eb011e3": {"client": "Malware", "version": "Emotet"},
            "6734f37431670b3ab4292b8f60f29984": {"client": "Malware", "version": "Dridex"}
        }

        return ja3_db.get(ja3_hash, {"client": "Unknown", "version": "Unknown"})


class CertificateAnalyzer:
    """SSL/TLS 证书分析"""

    @staticmethod
    def extract_certificate_info(packet):
        """
        从数据包中提取证书信息

        Args:
            packet: 包含 TLS Certificate 的数据包

        Returns:
            dict: 证书信息
        """
        if not TLS_LAYER_AVAILABLE:
            return None

        try:
            if not packet.haslayer(TLS) or not packet.haslayer(TLSCertificate):
                return None

            cert_info = {
                "issuer": None,
                "subject": None,
                "valid_from": None,
                "valid_to": None,
                "serial_number": None,
                "is_expired": None,
                "is_self_signed": None,
                "signature_algorithm": None,
                "public_key_algorithm": None,
                "key_size": None,
                "san": []  # Subject Alternative Names
            }

            # 使用 cryptography 库进行详细分析
            if CRYPTOGRAPHY_AVAILABLE and hasattr(packet[TLSCertificate], 'certs') and packet[TLSCertificate].certs:
                cert_data = packet[TLSCertificate].certs[0]
                cert = x509.load_der_x509_certificate(cert_data, default_backend())

                # 基本信息
                cert_info["issuer"] = str(cert.issuer)
                cert_info["subject"] = str(cert.subject)
                cert_info["valid_from"] = cert.not_valid_before.isoformat()
                cert_info["valid_to"] = cert.not_valid_after.isoformat()
                cert_info["serial_number"] = format(cert.serial_number, 'x')

                # 有效期检查
                now = datetime.datetime.now()
                cert_info["is_expired"] = now > cert.not_valid_after or now < cert.not_valid_before

                # 自签名检查
                cert_info["is_self_signed"] = cert.issuer == cert.subject

                # 算法信息
                cert_info["signature_algorithm"] = cert.signature_algorithm_oid._name
                cert_info["public_key_algorithm"] = cert.public_key().__class__.__name__

                # 密钥大小
                if hasattr(cert.public_key(), 'key_size'):
                    cert_info["key_size"] = cert.public_key().key_size

                # 主题备用名称 (SAN)
                try:
                    san_ext = cert.extensions.get_extension_for_oid(x509.ExtensionOID.SUBJECT_ALTERNATIVE_NAME)
                    if san_ext:
                        for name in san_ext.value:
                            if isinstance(name, x509.DNSName):
                                cert_info["san"].append(("DNS", name.value))
                            elif isinstance(name, x509.IPAddress):
                                cert_info["san"].append(("IP", str(name.value)))
                except x509.ExtensionNotFound:
                    pass

            return cert_info

        except Exception as e:
            logger.error(f"提取证书信息时出错: {str(e)}")
            return None

    @staticmethod
    def check_certificate_validity(cert_info):
        """
        检查证书有效性

        Args:
            cert_info: 证书信息

        Returns:
            dict: 证书有效性检查结果
        """
        if not cert_info:
            return None

        result = {
            "is_valid": True,
            "issues": []
        }

        # 检查是否过期
        if cert_info.get("is_expired"):
            result["is_valid"] = False
            result["issues"].append("证书已过期或尚未生效")

        # 检查是否自签名
        if cert_info.get("is_self_signed"):
            result["is_valid"] = False
            result["issues"].append("自签名证书")

        # 检查密钥长度
        key_size = cert_info.get("key_size")
        if key_size:
            if "RSA" in cert_info.get("public_key_algorithm", "") and key_size < 2048:
                result["is_valid"] = False
                result["issues"].append(f"RSA 密钥长度不足 (当前: {key_size}, 推荐: >=2048)")
            elif "EllipticCurve" in cert_info.get("public_key_algorithm", "") and key_size < 256:
                result["is_valid"] = False
                result["issues"].append(f"椭圆曲线密钥长度不足 (当前: {key_size}, 推荐: >=256)")

        # 检查签名算法
        sig_alg = cert_info.get("signature_algorithm", "").lower()
        if "md5" in sig_alg or "sha1" in sig_alg:
            result["is_valid"] = False
            result["issues"].append(f"弱签名算法: {cert_info.get('signature_algorithm')}")

        return result


class ALPNAnalyzer:
    """ALPN (Application-Layer Protocol Negotiation) 协议解析"""

    @staticmethod
    def extract_alpn_info(packet):
        """
        从数据包中提取 ALPN 信息

        Args:
            packet: 包含 TLS ClientHello/ServerHello 的数据包

        Returns:
            list: 支持的 ALPN 协议列表
        """
        if not TLS_LAYER_AVAILABLE:
            return None

        try:
            alpn_protocols = []

            # 检查 ClientHello
            if packet.haslayer(TLSClientHello) and hasattr(packet[TLSClientHello], 'ext'):
                for ext in packet[TLSClientHello].ext:
                    if ext.type == 16:  # ALPN 扩展类型
                        if hasattr(ext, 'alpn_protocols'):
                            for proto in ext.alpn_protocols:
                                proto_name = ALPN_PROTOCOLS.get(proto, proto.decode('utf-8', errors='ignore'))
                                alpn_protocols.append({"protocol": proto_name, "raw": proto.hex()})

            # 检查 ServerHello
            elif packet.haslayer(TLSServerHello) and hasattr(packet[TLSServerHello], 'ext'):
                for ext in packet[TLSServerHello].ext:
                    if ext.type == 16:  # ALPN 扩展类型
                        if hasattr(ext, 'alpn_protocol'):
                            proto = ext.alpn_protocol
                            proto_name = ALPN_PROTOCOLS.get(proto, proto.decode('utf-8', errors='ignore'))
                            alpn_protocols.append({"protocol": proto_name, "raw": proto.hex(), "selected": True})

            return alpn_protocols if alpn_protocols else None

        except Exception as e:
            logger.error(f"提取 ALPN 信息时出错: {str(e)}")
            return None

    @staticmethod
    def is_http2(packet):
        """
        检查是否是 HTTP/2 流量

        Args:
            packet: 数据包

        Returns:
            bool: 是否是 HTTP/2 流量
        """
        alpn_info = ALPNAnalyzer.extract_alpn_info(packet)
        if alpn_info:
            for proto in alpn_info:
                if proto.get("protocol") == "HTTP/2" or proto.get("raw") == "6832":
                    return True
        return False


class EncryptionFeatureExtractor:
    """加密流量特征提取"""

    @staticmethod
    def extract_encryption_features(packet):
        """
        提取加密流量特征

        Args:
            packet: 数据包

        Returns:
            dict: 加密特征
        """
        features = {}

        # 检查参数是否有效
        if packet is None:
            return features

        # 基本 TLS 信息
        if TLS_LAYER_AVAILABLE and packet.haslayer(TLS):
            # TLS 版本
            if packet.haslayer(TLSClientHello):
                version = packet[TLSClientHello].version
                features["tls_version"] = TLS_VERSIONS.get(version, f"Unknown (0x{version:04x})")

                # 密码套件数量
                if hasattr(packet[TLSClientHello], 'ciphers'):
                    features["cipher_count"] = len(packet[TLSClientHello].ciphers)

                # 扩展数量
                if hasattr(packet[TLSClientHello], 'ext'):
                    features["extension_count"] = len(packet[TLSClientHello].ext)

            # JA3 指纹
            ja3_hash, ja3_str = JA3Calculator.calculate_ja3(packet)
            if ja3_hash:
                features["ja3_hash"] = ja3_hash
                features["ja3_str"] = ja3_str

                # 获取 JA3 指纹信息
                ja3_info = JA3Calculator.get_ja3_info(ja3_hash)
                features["client_info"] = ja3_info

            # JA3S 指纹
            ja3s_hash, ja3s_str = JA3Calculator.calculate_ja3s(packet)
            if ja3s_hash:
                features["ja3s_hash"] = ja3s_hash
                features["ja3s_str"] = ja3s_str

            # 证书信息
            cert_info = CertificateAnalyzer.extract_certificate_info(packet)
            if cert_info:
                features["certificate"] = cert_info

                # 证书有效性检查
                cert_validity = CertificateAnalyzer.check_certificate_validity(cert_info)
                if cert_validity:
                    features["certificate_validity"] = cert_validity

            # ALPN 信息
            alpn_info = ALPNAnalyzer.extract_alpn_info(packet)
            if alpn_info:
                features["alpn"] = alpn_info
                features["is_http2"] = ALPNAnalyzer.is_http2(packet)

        # 数据包长度分布
        if hasattr(packet, 'len'):
            features["packet_length"] = packet.len

        # 负载熵 (如果有负载)
        if hasattr(packet, 'payload') and hasattr(packet.payload, 'load'):
            try:
                payload = bytes(packet.payload.load)
                if payload:
                    # 计算熵
                    entropy = EncryptionFeatureExtractor.calculate_entropy(payload)
                    features["payload_entropy"] = entropy

                    # 加密流量通常熵值较高
                    features["likely_encrypted"] = entropy > 7.0
            except:
                pass

        return features

    @staticmethod
    def calculate_entropy(data):
        """
        计算数据的香农熵

        Args:
            data: 字节数据

        Returns:
            float: 熵值
        """
        if not data:
            return 0

        # 计算每个字节的频率
        freq = defaultdict(int)
        for byte in data:
            freq[byte] += 1

        # 计算熵
        entropy = 0
        for count in freq.values():
            p = count / len(data)
            entropy -= p * np.log2(p)

        return entropy


class EncryptionAnalyzer:
    """加密流量分析器 - 主类"""

    def __init__(self):
        """初始化加密流量分析器"""
        self.ja3_calculator = JA3Calculator()
        self.cert_analyzer = CertificateAnalyzer()
        self.alpn_analyzer = ALPNAnalyzer()
        self.feature_extractor = EncryptionFeatureExtractor()

    def analyze_packet(self, packet):
        """
        分析单个数据包

        Args:
            packet: 数据包

        Returns:
            dict: 分析结果
        """
        result = {
            "is_encrypted": False,
            "encryption_type": None,
            "features": {}
        }

        # 检查参数是否有效
        if packet is None:
            return result

        try:
            # 检查是否是 TLS 流量
            if TLS_LAYER_AVAILABLE and hasattr(packet, 'haslayer') and packet.haslayer(TLS):
                result["is_encrypted"] = True
                result["encryption_type"] = "TLS/SSL"

                # 提取加密特征
                result["features"] = self.feature_extractor.extract_encryption_features(packet)

            # 检查是否是其他加密流量 (基于熵)
            elif hasattr(packet, 'payload') and hasattr(packet.payload, 'load'):
                try:
                    payload = bytes(packet.payload.load)
                    if payload:
                        entropy = self.feature_extractor.calculate_entropy(payload)
                        if entropy > 7.0:  # 高熵值通常表示加密或压缩数据
                            result["is_encrypted"] = True
                            result["encryption_type"] = "Unknown (High Entropy)"
                            result["features"]["payload_entropy"] = entropy
                except Exception as e:
                    logger.debug(f"计算负载熵时出错: {str(e)}")
        except Exception as e:
            logger.error(f"分析数据包时出错: {str(e)}")

        return result

    def analyze_pcap(self, pcap_file):
        """
        分析 PCAP 文件

        Args:
            pcap_file: PCAP 文件路径

        Returns:
            dict: 分析结果
        """
        # 初始化结果字典
        result = {
            "total_packets": 0,
            "encrypted_packets": 0,
            "encryption_types": defaultdict(int),
            "ja3_fingerprints": defaultdict(int),
            "certificate_issues": [],
            "alpn_protocols": defaultdict(int),
            "detailed_results": [],
            "encryption_ratio": 0.0,
            "entropy_data": [],  # 存储熵值数据
            "packet_sizes": [],  # 存储数据包大小
            "entropy_time_series": []  # 存储熵值时间序列
        }

        # 检查参数是否有效
        if not pcap_file or not os.path.exists(pcap_file):
            logger.error(f"PCAP 文件不存在: {pcap_file}")
            return {"error": f"PCAP 文件不存在: {pcap_file}"}

        try:
            # 读取PCAP文件
            try:
                packets = rdpcap(pcap_file)
                result["total_packets"] = len(packets)
            except Exception as e:
                logger.error(f"读取PCAP文件失败: {str(e)}")
                return {"error": f"读取PCAP文件失败: {str(e)}"}

            # 分析每个数据包
            for i, packet in enumerate(packets):
                try:
                    packet_result = self.analyze_packet(packet)

                    # 提取数据包大小
                    packet_size = len(packet) if hasattr(packet, '__len__') else 0
                    result["packet_sizes"].append(packet_size)

                    # 提取熵值数据
                    entropy = None
                    if "features" in packet_result and "payload_entropy" in packet_result["features"]:
                        entropy = packet_result["features"]["payload_entropy"]
                    elif hasattr(packet, 'payload') and hasattr(packet.payload, 'load'):
                        try:
                            payload = bytes(packet.payload.load)
                            if payload:
                                entropy = self.feature_extractor.calculate_entropy(payload)
                        except Exception as e:
                            logger.debug(f"计算数据包 {i+1} 的熵值时出错: {str(e)}")

                    # 存储熵值数据
                    if entropy is not None:
                        result["entropy_data"].append({
                            "packet_number": i + 1,
                            "entropy": entropy,
                            "is_encrypted": packet_result["is_encrypted"],
                            "size": packet_size
                        })

                        # 添加到时间序列
                        timestamp = packet.time if hasattr(packet, 'time') else i
                        result["entropy_time_series"].append({
                            "timestamp": timestamp,
                            "entropy": entropy,
                            "is_encrypted": packet_result["is_encrypted"]
                        })

                    # 更新统计信息
                    if packet_result["is_encrypted"]:
                        result["encrypted_packets"] += 1
                        result["encryption_types"][packet_result["encryption_type"]] += 1

                        # JA3 指纹统计
                        ja3_hash = packet_result["features"].get("ja3_hash")
                        if ja3_hash:
                            result["ja3_fingerprints"][ja3_hash] += 1

                        # 证书问题统计
                        cert_validity = packet_result["features"].get("certificate_validity")
                        if cert_validity and not cert_validity.get("is_valid"):
                            for issue in cert_validity.get("issues", []):
                                result["certificate_issues"].append({
                                    "packet_number": i + 1,
                                    "issue": issue
                                })

                        # ALPN 协议统计
                        alpn_info = packet_result["features"].get("alpn")
                        if alpn_info:
                            for proto in alpn_info:
                                result["alpn_protocols"][proto.get("protocol")] += 1

                    # 添加到详细结果
                    result["detailed_results"].append({
                        "packet_number": i + 1,
                        "result": packet_result
                    })
                except Exception as e:
                    logger.warning(f"分析数据包 {i+1} 时出错: {str(e)}")
                    # 继续处理下一个数据包
                    continue

            # 计算加密比例
            result["encryption_ratio"] = result["encrypted_packets"] / result["total_packets"] if result["total_packets"] > 0 else 0

            return result

        except Exception as e:
            logger.error(f"分析 PCAP 文件时出错: {str(e)}")
            return {"error": str(e)}

    def get_encryption_stats(self, packets):
        """
        获取加密流量统计信息

        Args:
            packets: 数据包列表

        Returns:
            dict: 统计信息
        """
        stats = {
            "total_packets": len(packets),
            "encrypted_packets": 0,
            "encryption_types": defaultdict(int),
            "tls_versions": defaultdict(int),
            "ja3_fingerprints": defaultdict(int),
            "certificate_issues": [],
            "alpn_protocols": defaultdict(int)
        }

        for i, packet in enumerate(packets):
            packet_result = self.analyze_packet(packet)

            if packet_result["is_encrypted"]:
                stats["encrypted_packets"] += 1
                stats["encryption_types"][packet_result["encryption_type"]] += 1

                # TLS 版本统计
                tls_version = packet_result["features"].get("tls_version")
                if tls_version:
                    stats["tls_versions"][tls_version] += 1

                # JA3 指纹统计
                ja3_hash = packet_result["features"].get("ja3_hash")
                if ja3_hash:
                    client_info = packet_result["features"].get("client_info", {})
                    stats["ja3_fingerprints"][ja3_hash] = {
                        "count": stats["ja3_fingerprints"].get(ja3_hash, {}).get("count", 0) + 1,
                        "client": client_info.get("client"),
                        "version": client_info.get("version")
                    }

                # 证书问题统计
                cert_validity = packet_result["features"].get("certificate_validity")
                if cert_validity and not cert_validity.get("is_valid"):
                    for issue in cert_validity.get("issues", []):
                        stats["certificate_issues"].append({
                            "packet_number": i + 1,
                            "issue": issue
                        })

                # ALPN 协议统计
                alpn_info = packet_result["features"].get("alpn")
                if alpn_info:
                    for proto in alpn_info:
                        stats["alpn_protocols"][proto.get("protocol")] += 1

        # 计算加密比例
        stats["encryption_ratio"] = stats["encrypted_packets"] / stats["total_packets"] if stats["total_packets"] > 0 else 0

        return stats
