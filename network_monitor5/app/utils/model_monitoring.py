"""
模型监控模块 - 提供模型性能监控和自动触发增量训练功能

包含功能:
- 性能指标跟踪
- 性能下降检测
- 自动增量训练触发
- 模型版本管理
"""

import numpy as np
import torch
import logging
import time
import json
import os
import threading
import queue
from datetime import datetime
from collections import deque
import matplotlib.pyplot as plt
import io
import base64

logger = logging.getLogger(__name__)

class ModelPerformanceTracker:
    """模型性能跟踪器"""

    def __init__(self, window_size=100, threshold=0.05, metrics=None, model_path=None):
        """
        初始化模型性能跟踪器

        Args:
            window_size: 滑动窗口大小
            threshold: 性能下降阈值
            metrics: 要跟踪的指标列表
            model_path: 模型路径（用于增量训练）
        """
        self.window_size = window_size
        self.threshold = threshold
        self.metrics = metrics or ['accuracy', 'latency']
        self.model_path = model_path

        # 初始化指标跟踪器
        self.metric_history = {metric: deque(maxlen=window_size) for metric in self.metrics}
        self.baseline_values = {metric: None for metric in self.metrics}

        # 性能下降计数器
        self.decline_counter = 0
        self.consecutive_decline_threshold = 5  # 连续下降5次触发警报

        # 统计信息
        self.total_predictions = 0
        self.correct_predictions = 0
        self.total_latency = 0

        # 历史记录
        self.history = []

        # 增量训练触发器
        self.training_triggered = False
        self.training_in_progress = False
        self.training_queue = queue.Queue()

        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()

    def update(self, prediction, ground_truth=None, latency=None, features=None):
        """
        更新性能指标

        Args:
            prediction: 模型预测结果
            ground_truth: 真实标签（可选）
            latency: 预测延迟（可选）
            features: 输入特征（可选，用于增量训练）
        """
        # 更新统计信息
        self.total_predictions += 1

        # 更新准确率
        if ground_truth is not None:
            is_correct = prediction == ground_truth
            self.correct_predictions += int(is_correct)
            current_accuracy = self.correct_predictions / self.total_predictions
            self.metric_history['accuracy'].append(current_accuracy)

        # 更新延迟
        if latency is not None:
            self.total_latency += latency
            current_latency = latency  # 使用当前延迟，而不是平均延迟
            self.metric_history['latency'].append(current_latency)

        # 保存特征和标签用于增量训练
        if features is not None and ground_truth is not None:
            self.training_queue.put((features, ground_truth))
            if self.training_queue.qsize() > 1000:  # 限制队列大小
                try:
                    self.training_queue.get_nowait()
                except queue.Empty:
                    pass

        # 检查性能下降
        self._check_performance_decline()

        # 记录历史
        self.history.append({
            'timestamp': datetime.now().isoformat(),
            'metrics': {
                metric: list(self.metric_history[metric])[-1] if self.metric_history[metric] else None
                for metric in self.metrics
            },
            'total_predictions': self.total_predictions
        })

    def _check_performance_decline(self):
        """检查性能是否下降"""
        # 至少需要一定数量的样本才能检测
        if self.total_predictions < 50:
            return

        # 初始化基准值
        for metric in self.metrics:
            if self.baseline_values[metric] is None and len(self.metric_history[metric]) >= self.window_size // 2:
                self.baseline_values[metric] = np.mean(list(self.metric_history[metric]))
                logger.info(f"初始化{metric}基准值: {self.baseline_values[metric]:.4f}")

        # 检查性能下降
        decline_detected = False

        # 检查准确率下降
        if 'accuracy' in self.metrics and self.baseline_values['accuracy'] is not None:
            current_accuracy = list(self.metric_history['accuracy'])[-1]
            if current_accuracy < self.baseline_values['accuracy'] - self.threshold:
                decline_detected = True
                logger.warning(f"检测到准确率下降: {current_accuracy:.4f} < {self.baseline_values['accuracy']:.4f} - {self.threshold}")

        # 检查延迟增加
        if 'latency' in self.metrics and self.baseline_values['latency'] is not None:
            current_latency = list(self.metric_history['latency'])[-1]
            if current_latency > self.baseline_values['latency'] * (1 + self.threshold):
                decline_detected = True
                logger.warning(f"检测到延迟增加: {current_latency:.4f} > {self.baseline_values['latency']:.4f} * (1 + {self.threshold})")

        # 更新下降计数器
        if decline_detected:
            self.decline_counter += 1
            if self.decline_counter >= self.consecutive_decline_threshold:
                self._trigger_retraining()
        else:
            self.decline_counter = 0

    def _trigger_retraining(self):
        """触发模型重训练"""
        if self.training_triggered or self.training_in_progress:
            return

        logger.warning(f"检测到连续{self.consecutive_decline_threshold}次性能下降，触发模型重训练")
        self.training_triggered = True

        # 启动重训练线程
        training_thread = threading.Thread(target=self._retrain_model)
        training_thread.start()

    def _retrain_model(self):
        """重训练模型"""
        self.training_in_progress = True

        try:
            logger.info("开始增量训练模型")

            # 收集训练数据
            features_list = []
            labels_list = []

            # 从队列中获取数据
            while not self.training_queue.empty():
                features, label = self.training_queue.get()
                features_list.append(features)
                labels_list.append(label)

            # 确保有足够的数据
            if len(features_list) < 50:
                logger.warning("训练数据不足，取消增量训练")
                self.training_triggered = False
                self.training_in_progress = False
                return

            # 转换为张量
            if isinstance(features_list[0], torch.Tensor):
                features_tensor = torch.stack(features_list)
                labels_tensor = torch.tensor(labels_list)
            else:
                features_tensor = torch.tensor(np.array(features_list))
                labels_tensor = torch.tensor(np.array(labels_list))

            # 创建数据集和加载器
            from torch.utils.data import TensorDataset, DataLoader
            dataset = TensorDataset(features_tensor, labels_tensor)
            data_loader = DataLoader(dataset, batch_size=32, shuffle=True)

            # 加载模型
            if self.model_path and os.path.exists(self.model_path):
                # 这里需要根据实际情况加载模型
                # model = torch.load(self.model_path)
                logger.info(f"从{self.model_path}加载模型")
            else:
                logger.warning("模型路径不存在，无法进行增量训练")
                self.training_triggered = False
                self.training_in_progress = False
                return

            # 增量训练
            # 这里需要根据实际情况实现增量训练逻辑
            # model.train(data_loader, epochs=5)

            # 保存模型
            # torch.save(model, self.model_path)

            logger.info("增量训练完成，模型已更新")

            # 重置基准值
            for metric in self.metrics:
                self.baseline_values[metric] = None

            # 重置下降计数器
            self.decline_counter = 0

        except Exception as e:
            logger.error(f"增量训练失败: {str(e)}")

        finally:
            self.training_triggered = False
            self.training_in_progress = False

    def _monitoring_loop(self):
        """监控线程主循环"""
        while True:
            # 定期检查性能
            time.sleep(60)  # 每分钟检查一次

            # 如果有足够的数据，更新基准值
            for metric in self.metrics:
                if len(self.metric_history[metric]) >= self.window_size:
                    # 使用滑动窗口平均值作为新的基准
                    new_baseline = np.mean(list(self.metric_history[metric]))

                    # 如果基准值显著变化，更新它
                    if self.baseline_values[metric] is None or \
                       abs(new_baseline - self.baseline_values[metric]) / max(1e-10, self.baseline_values[metric]) > 0.1:
                        logger.info(f"更新{metric}基准值: {self.baseline_values[metric]:.4f} -> {new_baseline:.4f}")
                        self.baseline_values[metric] = new_baseline

    def get_metrics(self):
        """获取当前指标"""
        metrics = {}

        # 计算准确率
        if 'accuracy' in self.metrics and self.metric_history['accuracy']:
            metrics['accuracy'] = list(self.metric_history['accuracy'])[-1]
            metrics['accuracy_history'] = list(self.metric_history['accuracy'])
        else:
            metrics['accuracy'] = self.correct_predictions / max(1, self.total_predictions)

        # 计算平均延迟
        if 'latency' in self.metrics and self.metric_history['latency']:
            metrics['latency'] = list(self.metric_history['latency'])[-1]
            metrics['latency_history'] = list(self.metric_history['latency'])
        else:
            metrics['latency'] = self.total_latency / max(1, self.total_predictions)

        # 添加其他统计信息
        metrics['total_predictions'] = self.total_predictions
        metrics['decline_counter'] = self.decline_counter
        metrics['training_triggered'] = self.training_triggered
        metrics['training_in_progress'] = self.training_in_progress

        return metrics

    def get_performance_chart(self):
        """生成性能图表"""
        try:
            if not self.history:
                print("没有历史数据，无法生成图表")
                return None

            # 创建图表
            plt.figure(figsize=(10, 6))

            # 准确率图表
            if 'accuracy' in self.metrics and any(h['metrics'].get('accuracy') is not None for h in self.history):
                plt.subplot(2, 1, 1)
                accuracy_values = [h['metrics'].get('accuracy', None) for h in self.history]
                accuracy_values = [v for v in accuracy_values if v is not None]
                if accuracy_values:  # 确保有数据再绘图
                    plt.plot(accuracy_values, 'b-', label='Accuracy')
                    if self.baseline_values['accuracy'] is not None:
                        plt.axhline(y=self.baseline_values['accuracy'], color='r', linestyle='--', label='Baseline')
                        plt.axhline(y=self.baseline_values['accuracy'] - self.threshold, color='y', linestyle='--', label='Threshold')
                    plt.title('Model Accuracy')
                    plt.ylabel('Accuracy')
                    plt.legend()

            # 延迟图表
            if 'latency' in self.metrics and any(h['metrics'].get('latency') is not None for h in self.history):
                plt.subplot(2, 1, 2)
                latency_values = [h['metrics'].get('latency', None) for h in self.history]
                latency_values = [v for v in latency_values if v is not None]
                if latency_values:  # 确保有数据再绘图
                    plt.plot(latency_values, 'g-', label='Latency')
                    if self.baseline_values['latency'] is not None:
                        plt.axhline(y=self.baseline_values['latency'], color='r', linestyle='--', label='Baseline')
                        plt.axhline(y=self.baseline_values['latency'] * (1 + self.threshold), color='y', linestyle='--', label='Threshold')
                    plt.title('Model Latency')
                    plt.ylabel('Latency (ms)')
                    plt.legend()

            plt.tight_layout()

            # 将图表转换为base64编码的字符串
            buf = io.BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close()

            return img_str
        except Exception as e:
            print(f"生成性能图表错误: {str(e)}")
            return None


class ModelMonitor:
    """模型监控器 - 单例模式"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(ModelMonitor, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, model_paths=None, window_size=100, threshold=0.05):
        if self._initialized:
            return

        self.model_paths = model_paths or {}
        self.trackers = {}

        # 为每个模型创建跟踪器
        for model_name, model_path in self.model_paths.items():
            self.trackers[model_name] = ModelPerformanceTracker(
                window_size=window_size,
                threshold=threshold,
                model_path=model_path
            )

        self._initialized = True

    def update(self, model_name, prediction, ground_truth=None, latency=None, features=None):
        """更新模型性能指标"""
        if model_name not in self.trackers:
            self.trackers[model_name] = ModelPerformanceTracker(model_path=self.model_paths.get(model_name))

        self.trackers[model_name].update(prediction, ground_truth, latency, features)

    def get_metrics(self, model_name=None):
        """获取模型性能指标"""
        # 检查是否已初始化
        if not hasattr(self, 'trackers') or not self.trackers:
            print("模型监控器未初始化或跟踪器为空")
            return {} if model_name is None else None

        if model_name:
            if model_name in self.trackers:
                return self.trackers[model_name].get_metrics()
            else:
                print(f"模型 {model_name} 不存在")
                return None
        else:
            return {name: tracker.get_metrics() for name, tracker in self.trackers.items()}

    def get_performance_chart(self, model_name):
        """获取模型性能图表"""
        # 检查是否已初始化
        if not hasattr(self, 'trackers') or not self.trackers:
            print("模型监控器未初始化或跟踪器为空")
            return None

        if model_name in self.trackers:
            try:
                return self.trackers[model_name].get_performance_chart()
            except Exception as e:
                print(f"获取模型 {model_name} 性能图表错误: {str(e)}")
                return None
        else:
            print(f"模型 {model_name} 不存在")
            return None


# 创建全局监控器实例
monitor = ModelMonitor()
