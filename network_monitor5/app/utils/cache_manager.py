import json
import os
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List, Set, Tuple
from ..models import PcapFile, AnalysisResult, db
import logging

logger = logging.getLogger(__name__)

class AnalysisCache:
    """
    PCAP文件分析结果缓存管理
    提供缓存和检索PCAP分析结果的功能，以减少重复分析的时间
    """
    
    # 缓存过期时间（小时）
    DEFAULT_CACHE_EXPIRY = 24
    
    @staticmethod
    def get_cached_result(pcap_file_id: int, analysis_type: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的分析结果
        
        Args:
            pcap_file_id: PCAP文件ID
            analysis_type: 分析类型 ('anomaly', 'encryption', 'flow', etc.)
            
        Returns:
            dict: 缓存的分析结果，如果没有则返回None
        """
        try:
            # 检查数据库中是否有缓存的结果
            cached_result = AnalysisResult.query.filter_by(
                pcap_file_id=pcap_file_id,
                analysis_type=analysis_type
            ).order_by(AnalysisResult.created_at.desc()).first()
            
            if cached_result:
                # 检查缓存是否过期
                expiry_time = datetime.utcnow() - timedelta(hours=AnalysisCache.DEFAULT_CACHE_EXPIRY)
                if cached_result.created_at >= expiry_time:
                    logger.info(f"Found cached analysis result for file {pcap_file_id}, type {analysis_type}")
                    return cached_result.get_result_data()
                else:
                    logger.info(f"Cached result expired for file {pcap_file_id}, type {analysis_type}")
            
            return None
        except Exception as e:
            logger.error(f"Error retrieving cached result: {str(e)}")
            return None
    
    @staticmethod
    def cache_result(pcap_file_id: int, analysis_type: str, result_data: Dict[str, Any]) -> AnalysisResult:
        """
        缓存分析结果
        
        Args:
            pcap_file_id: PCAP文件ID
            analysis_type: 分析类型 ('anomaly', 'encryption', 'flow', etc.)
            result_data: 要缓存的分析结果
            
        Returns:
            AnalysisResult: 创建的缓存记录
        """
        try:
            # 创建新的分析结果记录
            analysis_result = AnalysisResult(
                pcap_file_id=pcap_file_id,
                analysis_type=analysis_type
            )
            analysis_result.set_result_data(result_data)
            
            # 保存到数据库
            db.session.add(analysis_result)
            db.session.commit()
            
            logger.info(f"Cached analysis result for file {pcap_file_id}, type {analysis_type}")
            
            return analysis_result
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error caching result: {str(e)}")
            raise
    
    @staticmethod
    def invalidate_cache(pcap_file_id: int, analysis_type: Optional[str] = None) -> bool:
        """
        使指定PCAP文件的缓存失效
        
        Args:
            pcap_file_id: PCAP文件ID
            analysis_type: 分析类型，如果为None则使所有类型的缓存失效
            
        Returns:
            bool: 操作是否成功
        """
        try:
            query = AnalysisResult.query.filter_by(pcap_file_id=pcap_file_id)
            
            if analysis_type:
                query = query.filter_by(analysis_type=analysis_type)
            
            # 删除匹配的缓存记录
            count = query.delete()
            db.session.commit()
            
            logger.info(f"Invalidated {count} cached results for file {pcap_file_id}")
            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error invalidating cache: {str(e)}")
            return False
    
    @staticmethod
    def get_all_cached_results(pcap_file_id: int) -> Dict[str, Dict[str, Any]]:
        """
        获取指定PCAP文件的所有缓存结果
        
        Args:
            pcap_file_id: PCAP文件ID
            
        Returns:
            dict: 以分析类型为键，分析结果为值的字典
        """
        try:
            cached_results = AnalysisResult.query.filter_by(
                pcap_file_id=pcap_file_id
            ).order_by(AnalysisResult.analysis_type, AnalysisResult.created_at.desc()).all()
            
            # 按分析类型组织结果，每种类型只保留最新的
            result_by_type = {}
            seen_types = set()
            
            for result in cached_results:
                if result.analysis_type not in seen_types:
                    result_by_type[result.analysis_type] = result.get_result_data()
                    seen_types.add(result.analysis_type)
            
            return result_by_type
        except Exception as e:
            logger.error(f"Error retrieving all cached results: {str(e)}")
            return {}
    
    @staticmethod
    def cleanup_expired_cache(expiry_hours: int = DEFAULT_CACHE_EXPIRY) -> int:
        """
        清理过期的缓存记录
        
        Args:
            expiry_hours: 过期时间（小时）
            
        Returns:
            int: 删除的记录数
        """
        try:
            expiry_time = datetime.utcnow() - timedelta(hours=expiry_hours)
            count = AnalysisResult.query.filter(AnalysisResult.created_at < expiry_time).delete()
            db.session.commit()
            
            logger.info(f"Cleaned up {count} expired cache records")
            return count
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error cleaning up expired cache: {str(e)}")
            return 0


class FileMetadataCache:
    """
    PCAP文件元数据缓存
    存储文件基本信息、MD5哈希值等，用于快速检索和验证
    """
    
    @staticmethod
    def get_file_by_md5(md5_hash: str) -> Optional[PcapFile]:
        """
        通过MD5哈希值获取文件
        
        Args:
            md5_hash: 文件的MD5哈希值
            
        Returns:
            PcapFile: 文件对象，如果不存在则返回None
        """
        return PcapFile.query.filter_by(md5_hash=md5_hash).first()
    
    @staticmethod
    def find_duplicates() -> List[List[PcapFile]]:
        """
        查找所有重复文件（基于MD5哈希值）
        
        Returns:
            list: 重复文件组列表
        """
        # 查找所有有MD5哈希值的文件
        files_with_hash = PcapFile.query.filter(PcapFile.md5_hash.isnot(None)).all()
        
        # 按MD5哈希值分组
        hash_groups = {}
        for file in files_with_hash:
            if file.md5_hash not in hash_groups:
                hash_groups[file.md5_hash] = []
            hash_groups[file.md5_hash].append(file)
        
        # 返回重复文件组（有多个文件的组）
        return [group for group in hash_groups.values() if len(group) > 1]
    
    @staticmethod
    def update_file_metadata(file_id: int, metadata: Dict[str, Any]) -> bool:
        """
        更新文件元数据
        
        Args:
            file_id: 文件ID
            metadata: 要更新的元数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            file = PcapFile.query.get(file_id)
            if not file:
                return False
            
            # 更新元数据字段
            for key, value in metadata.items():
                if hasattr(file, key):
                    setattr(file, key, value)
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating file metadata: {str(e)}")
            return False