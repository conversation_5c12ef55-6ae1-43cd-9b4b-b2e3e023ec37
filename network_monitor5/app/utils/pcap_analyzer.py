import dpkt
import datetime
import socket
from scapy.all import *
import pandas as pd
import numpy as np
import psutil
import threading
import queue
import time
from datetime import datetime
import os
import json
from collections import defaultdict

class PcapAnalyzer:
    """PCAP文件分析器"""
    def __init__(self):
        self.flows = {}
        self.common_ports = {
            443: 'HTTPS',
            22: 'SSH',
            989: 'FTPS',
            990: 'FTPS',
            993: 'IMAPS',
            995: 'POP3S'
        }

    def get_realtime_traffic(self):
        """获取实时流量数据"""
        # 这里模拟实时数据，实际应用中需要替换为真实的网络接口捕获
        timestamps = pd.date_range(start='now', periods=100, freq='1s')
        traffic = np.random.randint(5000, 40000, size=100)
        return {
            'timestamps': timestamps.strftime('%Y-%m-%d %H:%M:%S').tolist(),
            'traffic': traffic.tolist()
        }

    def analyze_file_as_packets(self, file_path):
        """解析PCAP文件并返回单个数据包列表（用于加密检测）"""
        from scapy.all import rdpcap
        packets = rdpcap(file_path)
        packet_list = []

        # 处理所有数据包，不进行过滤
        for i, packet in enumerate(packets, 1):
            # 基本信息，无论是什么类型的包都添加
            packet_info = {
                'id': i,
                'number': i,
                'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
                'length': len(packet),
                'protocol': 'Unknown',
                'src_ip': '',
                'dst_ip': '',
                'src_port': 0,
                'dst_port': 0
            }

            # 提取IP层信息（如果存在）
            if IP in packet:
                packet_info['src_ip'] = packet[IP].src
                packet_info['dst_ip'] = packet[IP].dst

                # 提取TCP/UDP层信息（如果存在）
                if TCP in packet:
                    packet_info['protocol'] = 'TCP'
                    packet_info['src_port'] = packet[TCP].sport
                    packet_info['dst_port'] = packet[TCP].dport
                elif UDP in packet:
                    packet_info['protocol'] = 'UDP'
                    packet_info['src_port'] = packet[UDP].sport
                    packet_info['dst_port'] = packet[UDP].dport
                else:
                    # 其他IP协议
                    if packet[IP].proto == 1:
                        packet_info['protocol'] = 'ICMP'
                    else:
                        packet_info['protocol'] = f'IP({packet[IP].proto})'
            else:
                # 非IP协议包
                if packet.haslayer('ARP'):
                    packet_info['protocol'] = 'ARP'
                    if packet.haslayer('ARP'):
                        arp = packet.getlayer('ARP')
                        packet_info['src_ip'] = arp.psrc
                        packet_info['dst_ip'] = arp.pdst
                elif packet.haslayer('Ether'):
                    packet_info['protocol'] = 'Ethernet'
                    ether = packet.getlayer('Ether')
                    packet_info['src_ip'] = ether.src
                    packet_info['dst_ip'] = ether.dst

            # 所有数据包都添加到列表中
            packet_list.append(packet_info)

        # 打印统计信息用于调试
        print(f"PCAP文件已解析，共有 {len(packets)} 个数据包，处理后有 {len(packet_list)} 个数据包")

        return packet_list

    def analyze_file(self, file_path):
        """解析PCAP文件并返回流量数据（按流聚合）"""
        from scapy.all import rdpcap, IP, TCP, UDP
        packets = rdpcap(file_path)
        flows = {}

        for packet in packets:
            if IP in packet and (TCP in packet or UDP in packet):
                # 提取流量特征
                ip = packet[IP]
                proto = 'TCP' if TCP in packet else 'UDP'

                if proto == 'TCP':
                    sport = packet[TCP].sport
                    dport = packet[TCP].dport
                else:
                    sport = packet[UDP].sport
                    dport = packet[UDP].dport

                # 创建流标识
                flow_id = f"{ip.src}:{sport}-{ip.dst}:{dport}-{proto}"

                if flow_id not in flows:
                    flows[flow_id] = {
                        'src_ip': ip.src,
                        'dst_ip': ip.dst,
                        'src_port': sport,
                        'dst_port': dport,
                        'protocol': proto,
                        'packet_count': 0,
                        'byte_count': 0,
                        'start_time': packet.time,
                        'end_time': packet.time,
                        'length': len(packet)  # 添加长度字段
                    }

                flows[flow_id]['packet_count'] += 1
                flows[flow_id]['byte_count'] += len(packet)
                flows[flow_id]['end_time'] = packet.time

        return list(flows.values())

    @staticmethod
    def ip_to_str(ip_address):
        """将IP地址转换为字符串格式"""
        try:
            return socket.inet_ntop(socket.AF_INET, ip_address)
        except ValueError:
            return socket.inet_ntop(socket.AF_INET6, ip_address)

    def is_encrypted(self, packet):
        """检测数据包是否可能是加密流量"""
        if TCP in packet:
            dport = packet[TCP].dport
            sport = packet[TCP].sport
            if dport in self.common_ports:
                return True, self.common_ports[dport]
            if sport in self.common_ports:
                return True, self.common_ports[sport]
        return False, None

    def analyze_pcap(self, pcap_file):
        """分析PCAP文件"""
        try:
            packets = rdpcap(pcap_file)
            result = []

            for i, packet in enumerate(packets, 1):
                packet_info = self._extract_packet_info(packet, i)
                is_anomaly, attack_type = self._detect_anomaly(packet)

                packet_info['is_anomaly'] = is_anomaly
                packet_info['attack_type'] = attack_type

                result.append(packet_info)

            return result
        except Exception as e:
            raise Exception(f"分析PCAP文件失败: {str(e)}")

    def _extract_packet_info(self, packet, packet_number):
        """提取数据包信息"""
        info = {
            'id': packet_number,
            'number': packet_number,
            'time': datetime.fromtimestamp(packet.time).strftime('%Y-%m-%d %H:%M:%S.%f'),
            'length': len(packet),
            'protocol': 'Unknown',
            'src': '',
            'dst': '',
            'layers': {}
        }

        if IP in packet:
            info['src'] = packet[IP].src
            info['dst'] = packet[IP].dst
            info['protocol'] = packet[IP].proto
            info['layers']['ip'] = {
                'version': int(packet[IP].version),
                'ihl': int(packet[IP].ihl),
                'tos': int(packet[IP].tos),
                'len': int(packet[IP].len),
                'id': int(packet[IP].id),
                'flags': int(packet[IP].flags),
                'frag': int(packet[IP].frag),
                'ttl': int(packet[IP].ttl),
                'proto': int(packet[IP].proto)
            }

        if TCP in packet:
            info['protocol'] = 'TCP'
            info['layers']['tcp'] = {
                'sport': int(packet[TCP].sport),
                'dport': int(packet[TCP].dport),
                'seq': int(packet[TCP].seq),
                'ack': int(packet[TCP].ack),
                'flags': int(packet[TCP].flags)
            }
            # 添加TCP标志位的可读描述
            flags_desc = []
            if packet[TCP].flags.S: flags_desc.append('SYN')
            if packet[TCP].flags.A: flags_desc.append('ACK')
            if packet[TCP].flags.F: flags_desc.append('FIN')
            if packet[TCP].flags.R: flags_desc.append('RST')
            if packet[TCP].flags.P: flags_desc.append('PSH')
            info['layers']['tcp']['flags_desc'] = ' '.join(flags_desc)

        elif UDP in packet:
            info['protocol'] = 'UDP'
            info['layers']['udp'] = {
                'sport': int(packet[UDP].sport),
                'dport': int(packet[UDP].dport),
                'len': int(packet[UDP].len)
            }

        return info

    def _detect_anomaly(self, packet):
        """检测数据包是否异常"""
        # 检测SYN洪水攻击
        if TCP in packet and packet[TCP].flags == 2:  # SYN flag
            return True, 'SYN Flood'

        # 检测端口扫描
        if TCP in packet and packet[TCP].flags == 2 and packet[TCP].dport < 1024:
            return True, 'Port Scan'

        # 检测UDP洪水
        if UDP in packet and len(packet) > 1000:
            return True, 'UDP Flood'

        # 检测ICMP洪水
        if packet.haslayer('ICMP'):
            return True, 'ICMP Flood'

        return False, None

class PacketCapture:
    def __init__(self):
        self.is_capturing = False
        self.captured_packets = []
        self.total_bytes = 0
        self.protocol_stats = defaultdict(int)
        self.capture_thread = None
        self.bytes_history = []
        self.last_bytes = 0
        self.last_timestamp = time.time()
        self.anomaly_events = []
        self.protocol_flows = {}
        self.application_protocols = {
            80: 'HTTP',
            443: 'HTTPS',
            22: 'SSH',
            21: 'FTP',
            25: 'SMTP',
            53: 'DNS',
            110: 'POP3',
            143: 'IMAP',
            3306: 'MySQL',
            5432: 'PostgreSQL',
            8080: 'HTTP-ALT',
            8443: 'HTTPS-ALT'
        }

    def get_network_interfaces(self):
        """获取系统网络接口列表"""
        try:
            # 使用psutil获取网络接口信息
            interfaces = []
            for iface, addrs in psutil.net_if_addrs().items():
                # 获取IPv4地址
                ipv4 = next((addr.address for addr in addrs if addr.family == socket.AF_INET), None)
                interfaces.append({
                    'name': iface,
                    'description': f"{iface} ({ipv4})" if ipv4 else iface
                })
            return interfaces
        except Exception as e:
            print(f"获取网络接口失败: {str(e)}")
            return []

    def start_capture(self, interface):
        """开始捕获数据包"""
        if self.is_capturing:
            return False

        try:
            self.is_capturing = True
            # 在新线程中开始捕获
            self.capture_thread = threading.Thread(
                target=self._capture_packets,
                args=(interface,),
                daemon=True
            )
            self.capture_thread.start()
            return True
        except Exception as e:
            print(f"开始捕获失败: {str(e)}")
            self.is_capturing = False
            return False

    def _capture_packets(self, interface):
        """在后台线程中捕获数据包"""
        try:
            sniff(iface=interface,
                  prn=self._packet_callback,
                  stop_filter=lambda x: not self.is_capturing)
        except Exception as e:
            print(f"捕获数据包时出错: {str(e)}")
            self.is_capturing = False

    def stop_capture(self):
        """停止捕获数据包"""
        self.is_capturing = False
        if self.capture_thread:
            self.capture_thread.join(timeout=1.0)
        return True

    def _packet_callback(self, packet):
        """处理捕获的数据包"""
        if not self.is_capturing:
            return

        self.captured_packets.append(packet)
        packet_size = len(packet)
        self.total_bytes += packet_size

        # 检测异常
        is_anomaly = False
        attack_type = None

        # 简化的异常检测逻辑
        if IP in packet:
            # 检测 SYN 洪水攻击
            if TCP in packet and packet[TCP].flags == 2:  # SYN flag
                is_anomaly = True
                attack_type = 'SYN Flood'
            # 检测端口扫描
            elif TCP in packet and packet[TCP].flags == 2 and packet[TCP].dport < 1024:
                is_anomaly = True
                attack_type = 'Port Scan'
            # 检测 UDP 洪水
            elif UDP in packet and packet_size > 1000:
                is_anomaly = True
                attack_type = 'UDP Flood'
            # 检测 ICMP 洪水
            elif packet.haslayer('ICMP'):
                is_anomaly = True
                attack_type = 'ICMP Flood'

        if is_anomaly:
            # 记录异常事件
            event = {
                'time': datetime.now().strftime('%H:%M:%S'),
                'type': attack_type,
                'impact': self._get_impact_description(attack_type),
                'packet_id': len(self.captured_packets) - 1
            }
            self.anomaly_events.append(event)
            # 只保留最近10个异常事件
            if len(self.anomaly_events) > 10:
                self.anomaly_events.pop(0)

        # 更新协议统计 - 增强版本以捕获所有类型的数据包
        if IP in packet:
            proto = packet[IP].proto
            if proto == 6:  # TCP
                self.protocol_stats['TCP'] += 1
                if TCP in packet:
                    # 提取应用层协议
                    src_port = packet[TCP].sport
                    dst_port = packet[TCP].dport
                    src_proto = self.application_protocols.get(src_port, 'TCP')
                    dst_proto = self.application_protocols.get(dst_port, 'TCP')

                    # 更新协议流向统计
                    self._update_protocol_flow(src_proto, dst_proto, packet_size)
            elif proto == 17:  # UDP
                self.protocol_stats['UDP'] += 1
                if UDP in packet:
                    # 提取应用层协议
                    src_port = packet[UDP].sport
                    dst_port = packet[UDP].dport
                    src_proto = self.application_protocols.get(src_port, 'UDP')
                    dst_proto = self.application_protocols.get(dst_port, 'UDP')

                    # 更新协议流向统计
                    self._update_protocol_flow(src_proto, dst_proto, packet_size)
            elif proto == 1:  # ICMP
                self.protocol_stats['ICMP'] += 1
                self._update_protocol_flow('IP', 'ICMP', packet_size)
            elif proto == 2:  # IGMP
                self.protocol_stats['IGMP'] += 1
                self._update_protocol_flow('IP', 'IGMP', packet_size)
            elif proto == 50:  # ESP (IPsec)
                self.protocol_stats['IPsec'] += 1
                self._update_protocol_flow('IP', 'IPsec', packet_size)
            elif proto == 51:  # AH (IPsec)
                self.protocol_stats['IPsec'] += 1
                self._update_protocol_flow('IP', 'IPsec', packet_size)
            elif proto == 47:  # GRE
                self.protocol_stats['GRE'] += 1
                self._update_protocol_flow('IP', 'GRE', packet_size)
            elif proto == 132:  # SCTP
                self.protocol_stats['SCTP'] += 1
                self._update_protocol_flow('IP', 'SCTP', packet_size)
            else:
                self.protocol_stats['Other IP'] += 1
                self._update_protocol_flow('IP', f'Proto-{proto}', packet_size)
        elif packet.haslayer('ARP'):
            self.protocol_stats['ARP'] += 1
            self._update_protocol_flow('L2', 'ARP', packet_size)
        elif packet.haslayer('Ether'):
            eth_type = packet.getlayer('Ether').type
            if eth_type == 0x0806:  # ARP
                self.protocol_stats['ARP'] += 1
                self._update_protocol_flow('L2', 'ARP', packet_size)
            elif eth_type == 0x0800:  # IPv4 (already handled above)
                pass
            elif eth_type == 0x86DD:  # IPv6
                self.protocol_stats['IPv6'] += 1
                self._update_protocol_flow('L2', 'IPv6', packet_size)
            elif eth_type == 0x8100:  # VLAN
                self.protocol_stats['VLAN'] += 1
                self._update_protocol_flow('L2', 'VLAN', packet_size)
            else:
                self.protocol_stats['Other L2'] += 1
                self._update_protocol_flow('L2', f'Ether-{hex(eth_type)}', packet_size)
        else:
            # 捕获其他所有类型的数据包
            self.protocol_stats['Unknown'] += 1
            self._update_protocol_flow('Unknown', 'Unknown', packet_size)

        # 计算实时流量速率
        current_time = time.time()
        time_diff = current_time - self.last_timestamp
        if time_diff >= 1.0:  # 每秒更新一次
            bytes_diff = self.total_bytes - self.last_bytes
            bytes_per_second = int(bytes_diff / time_diff)

            # 记录历史数据
            self.bytes_history.append(bytes_per_second)
            # 只保留最近10分钟的数据
            if len(self.bytes_history) > 600:  # 10分钟 * 60秒
                self.bytes_history.pop(0)

            self.last_bytes = self.total_bytes
            self.last_timestamp = current_time

    def _update_protocol_flow(self, src_proto, dst_proto, packet_size):
        """更新协议流向统计"""
        # 跳过源和目标相同的流量
        if src_proto == dst_proto:
            return

        flow_key = f"{src_proto}->{dst_proto}"
        if flow_key not in self.protocol_flows:
            self.protocol_flows[flow_key] = {
                'source': src_proto,
                'target': dst_proto,
                'value': 0
            }
        self.protocol_flows[flow_key]['value'] += packet_size

    def _get_impact_description(self, attack_type):
        """根据攻击类型获取影响描述"""
        impact_map = {
            'SYN Flood': '网络连接资源耗尽',
            'Port Scan': '网络安全探测',
            'UDP Flood': '网络带宽耗尽',
            'ICMP Flood': '网络带宽耗尽'
        }
        return impact_map.get(attack_type, '未知影响')

    def get_capture_stats(self):
        """获取捕获统计信息"""
        # 计算当前流量速率
        current_bytes_per_second = 0
        if self.captured_packets:
            if len(self.bytes_history) > 0:
                current_bytes_per_second = self.bytes_history[-1]
            else:
                # 如果还没有历史数据，使用最后一个数据包的大小作为估计
                current_bytes_per_second = len(self.captured_packets[-1])

        # 准备协议流向数据
        protocol_flows_data = {
            'nodes': [],
            'links': []
        }

        # 定义协议颜色
        protocol_colors = {
            'HTTP': '#4CAF50',
            'HTTPS': '#2196F3',
            'DNS': '#FFC107',
            'TCP': '#9C27B0',
            'UDP': '#FF5722',
            'SSH': '#607D8B',
            'FTP': '#795548',
            'SMTP': '#E91E63',
            'POP3': '#9E9E9E',
            'IMAP': '#CDDC39',
            'HTTP-ALT': '#FF9800',
            'HTTPS-ALT': '#03A9F4'
        }

        # 如果没有数据，创建一些示例数据
        if not self.protocol_flows:
            # 示例协议节点
            sample_protocols = ['TCP', 'UDP', 'HTTP', 'HTTPS', 'DNS']

            # 创建节点
            for i, protocol in enumerate(sample_protocols):
                protocol_flows_data['nodes'].append({
                    'name': protocol,
                    'id': protocol,
                    'color': protocol_colors.get(protocol, '#000000')
                })

            # 创建示例连接
            sample_links = [
                {'source': 'TCP', 'target': 'HTTP', 'value': 50},
                {'source': 'TCP', 'target': 'HTTPS', 'value': 80},
                {'source': 'UDP', 'target': 'DNS', 'value': 30}
            ]

            for link in sample_links:
                protocol_flows_data['links'].append({
                    'source': link['source'],
                    'target': link['target'],
                    'value': link['value'],
                    'color': protocol_colors.get(link['source'], '#aaaaaa')
                })
        else:
            # 收集所有协议
            all_protocols = set()
            for flow_key, flow_data in self.protocol_flows.items():
                all_protocols.add(flow_data['source'])
                all_protocols.add(flow_data['target'])

            # 创建节点
            for protocol in all_protocols:
                protocol_flows_data['nodes'].append({
                    'name': protocol,
                    'id': protocol,
                    'color': protocol_colors.get(protocol, '#000000')
                })

            # 创建连接
            for flow_key, flow_data in self.protocol_flows.items():
                if flow_data['value'] > 0:  # 只添加有流量的连接
                    protocol_flows_data['links'].append({
                        'source': flow_data['source'],
                        'target': flow_data['target'],
                        'value': max(5, flow_data['value'] / 50),  # 缩放值以便于显示，确保最小值为5
                        'color': protocol_colors.get(flow_data['source'], '#aaaaaa')
                    })

        # 返回统计信息
        return {
            'success': True,
            'total_packets': len(self.captured_packets),
            'total_bytes': self.total_bytes,
            'protocol_stats': dict(self.protocol_stats),
            'bytes_per_second': current_bytes_per_second,
            'anomaly_events': self.anomaly_events,
            'protocol_flows': protocol_flows_data
        }

    def get_packet_detail(self, packet_id):
        """获取数据包详细信息"""
        try:
            packet = next(p for p in self.captured_packets if p['id'] == packet_id)
            return self._get_detailed_layers(packet)
        except StopIteration:
            return None

    def _get_detailed_layers(self, packet_info):
        """获取数据包各层详细信息"""
        layers = {}

        # 添加各层协议信息
        if 'src' in packet_info and 'dst' in packet_info:
            layers['Network Layer'] = {
                'Source IP': packet_info['src'],
                'Destination IP': packet_info['dst'],
                'Protocol': packet_info['protocol']
            }

        if 'src_port' in packet_info and 'dst_port' in packet_info:
            layers['Transport Layer'] = {
                'Source Port': packet_info['src_port'],
                'Destination Port': packet_info['dst_port']
            }

        return {'layers': layers}

    @staticmethod
    def _get_protocol_name(proto):
        """获取协议名称"""
        protocols = {
            1: 'ICMP',
            6: 'TCP',
            17: 'UDP'
        }
        return protocols.get(proto, str(proto))

    @staticmethod
    def _get_tcp_flags(tcp):
        """获取TCP标志位信息"""
        flags = []
        if tcp.flags.S: flags.append('SYN')
        if tcp.flags.A: flags.append('ACK')
        if tcp.flags.F: flags.append('FIN')
        if tcp.flags.R: flags.append('RST')
        if tcp.flags.P: flags.append('PSH')
        return '[' + ' '.join(flags) + ']'

    def _identify_application_protocol(self, packet):
        """识别应用层协议"""
        if TCP in packet:
            tcp = packet[TCP]
            # 常见端口映射
            port_map = {
                80: 'HTTP',
                443: 'HTTPS',
                21: 'FTP',
                22: 'SSH',
                23: 'Telnet',
                25: 'SMTP',
                53: 'DNS',
                110: 'POP3',
                143: 'IMAP',
                3306: 'MySQL',
                5432: 'PostgreSQL'
            }
            return port_map.get(tcp.dport, port_map.get(tcp.sport, 'TCP'))
        elif UDP in packet:
            udp = packet[UDP]
            port_map = {
                53: 'DNS',
                67: 'DHCP',
                68: 'DHCP',
                69: 'TFTP',
                123: 'NTP',
                161: 'SNMP',
                162: 'SNMP'
            }
            return port_map.get(udp.dport, port_map.get(udp.sport, 'UDP'))
        return 'Unknown'